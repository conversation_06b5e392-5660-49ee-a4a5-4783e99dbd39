/**
 * Admin Dashboard for Assessment Management
 * Handles both English and Mathematics assessment data
 */

class AdminDashboard {
  constructor() {
    this.currentTab = 'english';
    this.company = 'Birmingham';
    this.englishData = null;
    this.mathData = null;
    this.charts = {};
    
    this.init();
  }

  /**
   * Initialize the dashboard
   */
  init() {
    this.setupEventListeners();
    this.loadData();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Tab switching
    document.getElementById('english-tab').addEventListener('click', () => this.switchTab('english'));
    document.getElementById('math-tab').addEventListener('click', () => this.switchTab('math'));
    document.getElementById('overview-tab').addEventListener('click', () => this.switchTab('overview'));

    // Refresh data
    document.getElementById('refresh-data').addEventListener('click', () => this.loadData());

    // Company selection
    document.getElementById('company-select').addEventListener('change', (e) => {
      this.company = e.target.value;
      this.loadData();
    });

    // Modal close
    document.getElementById('close-modal').addEventListener('click', () => this.closeModal());
    document.getElementById('assessment-modal').addEventListener('click', (e) => {
      if (e.target.id === 'assessment-modal') {
        this.closeModal();
      }
    });
  }

  /**
   * Switch between tabs
   */
  switchTab(tab) {
    // Update tab buttons
    document.querySelectorAll('.assessment-tab').forEach(btn => {
      btn.classList.remove('active');
    });
    document.getElementById(`${tab}-tab`).classList.add('active');

    // Update panels
    document.querySelectorAll('.assessment-panel').forEach(panel => {
      panel.classList.add('hidden');
    });
    document.getElementById(`${tab}-panel`).classList.remove('hidden');

    this.currentTab = tab;

    // Load data for the current tab if needed
    if (tab === 'overview' && (this.englishData || this.mathData)) {
      this.updateOverviewPanel();
    }
  }

  /**
   * Load assessment data
   */
  async loadData() {
    this.showLoading();
    
    try {
      // Load both English and Math data in parallel
      const [englishData, mathData] = await Promise.all([
        this.loadEnglishData(),
        this.loadMathData()
      ]);

      this.englishData = englishData;
      this.mathData = mathData;

      // Update the current tab
      this.updateCurrentTab();
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.showError('Failed to load assessment data');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Load English assessment data
   */
  async loadEnglishData() {
    try {
      const baseUrl = window.location.protocol === 'file:' 
        ? 'http://localhost:3000' 
        : window.location.origin;

      const [analyticsResponse, responsesResponse] = await Promise.all([
        fetch(`${baseUrl}/api/admin/english-analytics?company=${this.company}`),
        fetch(`${baseUrl}/api/admin/english-responses?company=${this.company}&limit=20`)
      ]);

      if (!analyticsResponse.ok || !responsesResponse.ok) {
        throw new Error('Failed to fetch English assessment data');
      }

      const analytics = await analyticsResponse.json();
      const responses = await responsesResponse.json();

      return {
        analytics: analytics.data,
        responses: responses.data || []
      };
    } catch (error) {
      console.error('Error loading English data:', error);
      return { analytics: {}, responses: [] };
    }
  }

  /**
   * Load Mathematics assessment data
   */
  async loadMathData() {
    try {
      const baseUrl = window.location.protocol === 'file:' 
        ? 'http://localhost:3000' 
        : window.location.origin;

      const [analyticsResponse, responsesResponse] = await Promise.all([
        fetch(`${baseUrl}/api/admin/math-analytics?company=${this.company}`),
        fetch(`${baseUrl}/api/admin/math-responses?company=${this.company}&limit=20`)
      ]);

      if (!analyticsResponse.ok || !responsesResponse.ok) {
        throw new Error('Failed to fetch Mathematics assessment data');
      }

      const analytics = await analyticsResponse.json();
      const responses = await responsesResponse.json();

      return {
        analytics: analytics.data,
        responses: responses.data || []
      };
    } catch (error) {
      console.error('Error loading Math data:', error);
      return { analytics: {}, responses: [] };
    }
  }

  /**
   * Update the current tab with data
   */
  updateCurrentTab() {
    switch (this.currentTab) {
      case 'english':
        this.updateEnglishPanel();
        break;
      case 'math':
        this.updateMathPanel();
        break;
      case 'overview':
        this.updateOverviewPanel();
        break;
    }
  }

  /**
   * Update English assessment panel
   */
  updateEnglishPanel() {
    if (!this.englishData) return;

    const { analytics, responses } = this.englishData;

    // Update stats cards
    document.getElementById('english-total').textContent = analytics.totalAssessments || 0;
    document.getElementById('english-avg-score').textContent = 
      analytics.averageScores?.overall ? `${analytics.averageScores.overall}/21` : '-';
    document.getElementById('english-avg-duration').textContent = 
      analytics.averageDuration ? `${Math.round(analytics.averageDuration / 60)}min` : '-';
    document.getElementById('english-l2-count').textContent = 
      analytics.levelDistribution?.['L2/GCSE'] || 0;

    // Update level distribution chart
    this.updateEnglishChart(analytics.levelDistribution);

    // Update assessments table
    this.updateEnglishTable(responses);
  }

  /**
   * Update Mathematics assessment panel
   */
  updateMathPanel() {
    if (!this.mathData) return;

    const { analytics, responses } = this.mathData;

    // Update stats cards
    document.getElementById('math-total').textContent = analytics.totalAssessments || 0;
    document.getElementById('math-avg-score').textContent = 
      analytics.averageScores?.overall || '-';
    document.getElementById('math-avg-duration').textContent = 
      analytics.averageDuration ? `${Math.round(analytics.averageDuration / 60)}min` : '-';
    
    // Count GCSE level completions
    const gcseCount = (analytics.levelDistribution?.GCSEPart1 || 0) + 
                     (analytics.levelDistribution?.GCSEPart2 || 0);
    document.getElementById('math-gcse-count').textContent = gcseCount;

    // Update level distribution chart
    this.updateMathChart(analytics.levelDistribution);

    // Update assessments table
    this.updateMathTable(responses);
  }

  /**
   * Update overview panel
   */
  updateOverviewPanel() {
    const englishAnalytics = this.englishData?.analytics || {};
    const mathAnalytics = this.mathData?.analytics || {};

    // Update overview stats
    document.getElementById('overview-english-total').textContent = 
      englishAnalytics.totalAssessments || 0;
    document.getElementById('overview-math-total').textContent = 
      mathAnalytics.totalAssessments || 0;
    
    // Calculate total unique users (simplified - would need more complex logic for actual unique count)
    const totalUsers = (englishAnalytics.totalAssessments || 0) + (mathAnalytics.totalAssessments || 0);
    document.getElementById('overview-total-users').textContent = totalUsers;
    
    document.getElementById('overview-english-avg').textContent = 
      englishAnalytics.averageScores?.overall ? `${englishAnalytics.averageScores.overall}/21` : '-';
    document.getElementById('overview-math-avg').textContent = 
      mathAnalytics.averageScores?.overall || '-';

    // Update comparison chart
    this.updateComparisonChart();
  }

  /**
   * Update English level distribution chart
   */
  updateEnglishChart(levelDistribution) {
    const ctx = document.getElementById('english-level-chart').getContext('2d');
    
    if (this.charts.englishLevel) {
      this.charts.englishLevel.destroy();
    }

    this.charts.englishLevel = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Entry Level', 'L1', 'L2/GCSE'],
        datasets: [{
          data: [
            levelDistribution?.['Entry Level'] || 0,
            levelDistribution?.['L1'] || 0,
            levelDistribution?.['L2/GCSE'] || 0
          ],
          backgroundColor: ['#ef4444', '#f59e0b', '#10b981'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
  }

  /**
   * Update Mathematics level distribution chart
   */
  updateMathChart(levelDistribution) {
    const ctx = document.getElementById('math-level-chart').getContext('2d');
    
    if (this.charts.mathLevel) {
      this.charts.mathLevel.destroy();
    }

    this.charts.mathLevel = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Entry', 'Level 1', 'GCSE Part 1', 'GCSE Part 2'],
        datasets: [{
          label: 'Assessments Completed',
          data: [
            levelDistribution?.Entry || 0,
            levelDistribution?.Level1 || 0,
            levelDistribution?.GCSEPart1 || 0,
            levelDistribution?.GCSEPart2 || 0
          ],
          backgroundColor: '#3b82f6',
          borderColor: '#1d4ed8',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        },
        plugins: {
          legend: {
            display: false
          }
        }
      }
    });
  }

  /**
   * Update comparison chart
   */
  updateComparisonChart() {
    const ctx = document.getElementById('overview-comparison-chart').getContext('2d');
    
    if (this.charts.comparison) {
      this.charts.comparison.destroy();
    }

    const englishTotal = this.englishData?.analytics?.totalAssessments || 0;
    const mathTotal = this.mathData?.analytics?.totalAssessments || 0;

    this.charts.comparison = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['English Assessments', 'Mathematics Assessments'],
        datasets: [{
          label: 'Total Completed',
          data: [englishTotal, mathTotal],
          backgroundColor: ['#3b82f6', '#10b981'],
          borderColor: ['#1d4ed8', '#059669'],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        },
        plugins: {
          legend: {
            display: false
          }
        }
      }
    });
  }

  /**
   * Update English assessments table
   */
  updateEnglishTable(responses) {
    const tbody = document.getElementById('english-assessments-table');
    tbody.innerHTML = '';

    responses.forEach(response => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">${response.name || 'Unknown'}</div>
          <div class="text-sm text-gray-500">${response.userEmail}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${response.score || '-'}/21
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${this.getLevelBadgeClass(response.level)}">
            ${response.level || 'Unknown'}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${response.completedAt ? new Date(response.completedAt).toLocaleDateString() : '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button onclick="adminDashboard.viewEnglishDetails('${response.userEmail}')" 
                  class="text-blue-600 hover:text-blue-900">View Details</button>
        </td>
      `;
      tbody.appendChild(row);
    });
  }

  /**
   * Update Mathematics assessments table
   */
  updateMathTable(responses) {
    const tbody = document.getElementById('math-assessments-table');
    tbody.innerHTML = '';

    responses.forEach(response => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">${response.name || 'Unknown'}</div>
          <div class="text-sm text-gray-500">${response.userEmail}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${response.highestLevelCompleted || response.currentLevel || '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${response.overallScore || '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${response.completedAt ? new Date(response.completedAt).toLocaleDateString() : '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button onclick="adminDashboard.viewMathDetails('${response.userEmail}')" 
                  class="text-blue-600 hover:text-blue-900">View Details</button>
        </td>
      `;
      tbody.appendChild(row);
    });
  }

  /**
   * Get CSS class for level badge
   */
  getLevelBadgeClass(level) {
    switch (level) {
      case 'L2/GCSE':
        return 'bg-green-100 text-green-800';
      case 'L1':
        return 'bg-yellow-100 text-yellow-800';
      case 'Entry Level':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  /**
   * View English assessment details
   */
  async viewEnglishDetails(email) {
    try {
      const baseUrl = window.location.protocol === 'file:' 
        ? 'http://localhost:3000' 
        : window.location.origin;

      const response = await fetch(`${baseUrl}/api/admin/english-responses/${email}?company=${this.company}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch assessment details');
      }

      const data = await response.json();
      this.showAssessmentModal('English Assessment Details', data.data, 'english');
      
    } catch (error) {
      console.error('Error fetching English assessment details:', error);
      alert('Failed to load assessment details');
    }
  }

  /**
   * View Mathematics assessment details
   */
  async viewMathDetails(email) {
    try {
      const baseUrl = window.location.protocol === 'file:' 
        ? 'http://localhost:3000' 
        : window.location.origin;

      const response = await fetch(`${baseUrl}/api/admin/math-responses/${email}?company=${this.company}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch assessment details');
      }

      const data = await response.json();
      this.showAssessmentModal('Mathematics Assessment Details', data.data, 'math');
      
    } catch (error) {
      console.error('Error fetching Math assessment details:', error);
      alert('Failed to load assessment details');
    }
  }

  /**
   * Show assessment details modal
   */
  showAssessmentModal(title, data, type) {
    document.getElementById('modal-title').textContent = title;
    
    const content = document.getElementById('modal-content');
    content.innerHTML = this.generateModalContent(data, type);
    
    document.getElementById('assessment-modal').classList.remove('hidden');
  }

  /**
   * Generate modal content based on assessment type
   */
  generateModalContent(data, type) {
    if (type === 'english') {
      return `
        <div class="space-y-6">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <h4 class="font-semibold text-gray-900">User Information</h4>
              <p><strong>Name:</strong> ${data.name || 'Unknown'}</p>
              <p><strong>Email:</strong> ${data.userEmail}</p>
              <p><strong>Student Level:</strong> ${data.studentLevel || 'Not specified'}</p>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Assessment Results</h4>
              <p><strong>Score:</strong> ${data.score || '-'}/21</p>
              <p><strong>Level:</strong> ${data.level || 'Unknown'}</p>
              <p><strong>Completed:</strong> ${data.completedAt ? new Date(data.completedAt).toLocaleString() : '-'}</p>
            </div>
          </div>
          
          ${data.feedback ? `
            <div>
              <h4 class="font-semibold text-gray-900 mb-2">Feedback</h4>
              <div class="bg-gray-50 p-4 rounded">
                <p><strong>Grammar:</strong> ${data.feedback.grammar || 'No feedback available'}</p>
                <p><strong>Vocabulary:</strong> ${data.feedback.vocabulary || 'No feedback available'}</p>
                <p><strong>Coherence:</strong> ${data.feedback.coherence || 'No feedback available'}</p>
                <p><strong>Overall:</strong> ${data.feedback.overall || 'No feedback available'}</p>
              </div>
            </div>
          ` : ''}
          
          ${data.strengths && data.strengths.length > 0 ? `
            <div>
              <h4 class="font-semibold text-gray-900 mb-2">Strengths</h4>
              <ul class="list-disc list-inside bg-green-50 p-4 rounded">
                ${data.strengths.map(strength => `<li>${strength}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
          
          ${data.improvements && data.improvements.length > 0 ? `
            <div>
              <h4 class="font-semibold text-gray-900 mb-2">Areas for Improvement</h4>
              <ul class="list-disc list-inside bg-yellow-50 p-4 rounded">
                ${data.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      `;
    } else if (type === 'math') {
      return `
        <div class="space-y-6">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <h4 class="font-semibold text-gray-900">User Information</h4>
              <p><strong>Name:</strong> ${data.name || 'Unknown'}</p>
              <p><strong>Email:</strong> ${data.userEmail}</p>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Assessment Results</h4>
              <p><strong>Current Level:</strong> ${data.currentLevel || 'Unknown'}</p>
              <p><strong>Highest Level:</strong> ${data.highestLevelCompleted || 'None'}</p>
              <p><strong>Overall Score:</strong> ${data.overallScore || 0}</p>
              <p><strong>Completed:</strong> ${data.completedAt ? new Date(data.completedAt).toLocaleString() : '-'}</p>
            </div>
          </div>
          
          ${data.placementRecommendation ? `
            <div>
              <h4 class="font-semibold text-gray-900 mb-2">Placement Recommendation</h4>
              <div class="bg-blue-50 p-4 rounded">
                <p><strong>Recommended Level:</strong> ${data.placementRecommendation.level || 'Not specified'}</p>
                <p><strong>Reasoning:</strong> ${data.placementRecommendation.reasoning || 'No reasoning provided'}</p>
              </div>
            </div>
          ` : ''}
          
          ${data.strengths && data.strengths.length > 0 ? `
            <div>
              <h4 class="font-semibold text-gray-900 mb-2">Mathematical Strengths</h4>
              <ul class="list-disc list-inside bg-green-50 p-4 rounded">
                ${data.strengths.map(strength => `<li>${strength}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
          
          ${data.improvements && data.improvements.length > 0 ? `
            <div>
              <h4 class="font-semibold text-gray-900 mb-2">Areas for Development</h4>
              <ul class="list-disc list-inside bg-yellow-50 p-4 rounded">
                ${data.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      `;
    }
  }

  /**
   * Close modal
   */
  closeModal() {
    document.getElementById('assessment-modal').classList.add('hidden');
  }

  /**
   * Show loading state
   */
  showLoading() {
    document.getElementById('loading').classList.remove('hidden');
  }

  /**
   * Hide loading state
   */
  hideLoading() {
    document.getElementById('loading').classList.add('hidden');
  }

  /**
   * Show error message
   */
  showError(message) {
    alert(message); // Simple error handling - could be enhanced with better UI
  }
}

// Initialize dashboard when page loads
let adminDashboard;
document.addEventListener('DOMContentLoaded', function() {
  adminDashboard = new AdminDashboard();
});
