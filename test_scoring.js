// Test script to verify the scoring logic works correctly
require('dotenv').config();

// Mock answers structure as it comes from the frontend
const mockAnswers = [
  {
    questionId: 1,
    questionType: "multiple-choice",
    topic: "arithmetic",
    studentAnswer: "62",
    timeSpent: 5000
  },
  {
    questionId: 2,
    questionType: "multiple-choice", 
    topic: "arithmetic",
    studentAnswer: "55",
    timeSpent: 4000
  },
  {
    questionId: 3,
    questionType: "numeric",
    topic: "arithmetic", 
    studentAnswer: "34",
    timeSpent: 6000
  }
];

// Mock questions structure as generated by the server
const mockQuestions = [
  {
    id: 1,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 25 + 37?",
    options: ["52", "62", "72", "82"],
    correctAnswer: "62",
    points: 2,
    explanation: "25 + 37 = 62"
  },
  {
    id: 2,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 84 - 29?",
    options: ["45", "55", "65", "75"],
    correctAnswer: "55",
    points: 2,
    explanation: "84 - 29 = 55"
  },
  {
    id: 3,
    type: "numeric",
    topic: "arithmetic",
    question: "Calculate 15 + 28 - 9",
    correctAnswer: "34",
    points: 2,
    explanation: "15 + 28 - 9 = 43 - 9 = 34"
  }
];

// Test the fallback scoring logic
function testFallbackScoring() {
  console.log('Testing fallback scoring logic...');
  
  // Create a map of question IDs to questions for easy lookup
  const questionMap = {};
  mockQuestions.forEach(q => {
    questionMap[q.id] = q;
  });
  
  // Calculate score based on correct answers
  let score = 0;
  let correctCount = 0;
  
  mockAnswers.forEach((answer, index) => {
    const question = questionMap[answer.questionId];
    if (question && answer.studentAnswer) {
      const studentAns = String(answer.studentAnswer).trim().toLowerCase();
      const correctAns = String(question.correctAnswer).trim().toLowerCase();
      const points = question.points || 2;
      
      console.log(`Question ${index + 1}: Student="${studentAns}" vs Correct="${correctAns}" Points=${points}`);
      
      if (studentAns === correctAns) {
        score += points;
        correctCount++;
        console.log(`✅ Correct! Added ${points} points. Total: ${score}`);
      } else {
        console.log(`❌ Incorrect.`);
      }
    } else {
      console.log(`Question ${index + 1}: Missing question data or student answer`);
    }
  });

  console.log('\n=== FINAL RESULTS ===');
  console.log(`Total Score: ${score}`);
  console.log(`Correct Answers: ${correctCount}/${mockAnswers.length}`);
  console.log(`Expected Score: 6 (all correct)`);
  console.log(`Test ${score === 6 ? 'PASSED' : 'FAILED'}`);
}

// Test the analysis prompt creation
function testAnalysisPrompt() {
  console.log('\n=== Testing Analysis Prompt Creation ===');
  
  const questionSpecs = {
    count: 3,
    maxScore: 6,
    passingScore: 4
  };
  
  // Create a map of question IDs to questions for easy lookup
  const questionMap = {};
  mockQuestions.forEach(q => {
    questionMap[q.id] = q;
  });
  
  const studentResponses = mockAnswers.map((answer, index) => {
    const question = questionMap[answer.questionId];
    const questionText = question ? question.question : 'Question not found';
    const correctAnswer = question ? question.correctAnswer : 'Unknown';
    const points = question ? question.points : 2;
    
    return `Question ${index + 1}: "${questionText}" - Student Answer: "${answer.studentAnswer || 'No answer'}" - Correct Answer: "${correctAnswer}" - Topic: ${answer.topic || 'unknown'} - Points: ${points}`;
  }).join('\n');
  
  console.log('Generated student responses for AI prompt:');
  console.log(studentResponses);
  
  // Check if all required data is present
  const hasAllData = mockAnswers.every(answer => {
    const question = questionMap[answer.questionId];
    return question && question.correctAnswer && answer.studentAnswer;
  });
  
  console.log(`\nAll required data present: ${hasAllData ? 'YES' : 'NO'}`);
}

// Run tests
console.log('🧮 Testing Mathematics Assessment Scoring Logic\n');
testFallbackScoring();
testAnalysisPrompt();
