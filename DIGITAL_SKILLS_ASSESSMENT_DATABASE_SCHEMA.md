# Digital Skills Assessment Database Schema Documentation

## Overview
This document outlines the complete database structure for the digital skills assessment system, designed to mirror the mathematics assessment structure while accommodating digital skills-specific requirements including 7 competency levels, topic-based scoring, and comprehensive analytics.

## Database Structure

### Collection Path
```
companies/{companyId}/users/{userEmail}
```

### Complete Digital Skills Assessment Fields

#### Core Assessment Fields
```javascript
{
  // Basic assessment completion tracking
  digitalSkillsAssessmentCompleted: boolean,
  digitalSkillsCurrentLevel: string,              // "EntryLevel2", "EntryLevel2Plus", "Level1", "Level2", "EntryLevel3", "ICDLLevel2", "ICDLLevel3"
  digitalSkillsOverallScore: number,              // Total points across all completed levels
  digitalSkillsHighestLevelCompleted: string,     // Highest level successfully completed
  digitalSkillsAssessmentTimestamp: timestamp,    // When assessment was last updated
  totalTimeSpentOnDigitalSkills: number,         // Total time in seconds across all levels
  updatedAt: timestamp                           // Last update timestamp
}
```

#### Level-Specific Assessment Data
Each level stores detailed assessment results:

```javascript
{
  // Entry Level 2 - Computer Skills Beginners
  digitalSkillsEntryLevel2: {
    completed: boolean,
    score: number,                    // Points earned (0-30)
    passed: boolean,                  // true if score >= 12
    timeSpent: number,               // Time in seconds
    completedAt: timestamp,
    responses: [                     // Array of user responses
      {
        questionId: number,
        answer: string,
        isCorrect: boolean,
        timeSpent: number
      }
    ],
    topicBreakdown: {               // Performance by topic
      computerBasics: { correct: number, total: number, percentage: number },
      mouseKeyboard: { correct: number, total: number, percentage: number },
      basicOperations: { correct: number, total: number, percentage: number },
      fileManagement: { correct: number, total: number, percentage: number },
      basicSafety: { correct: number, total: number, percentage: number }
    }
  },

  // Entry Level 2/3 - Computer Skills Beginners Plus
  digitalSkillsEntryLevel2Plus: {
    completed: boolean,
    score: number,                    // Points earned (0-36)
    passed: boolean,                  // true if score >= 15
    timeSpent: number,
    completedAt: timestamp,
    responses: [...],
    topicBreakdown: {
      laptopDesktop: { correct: number, total: number, percentage: number },
      applications: { correct: number, total: number, percentage: number },
      internetSafety: { correct: number, total: number, percentage: number },
      emailBasics: { correct: number, total: number, percentage: number },
      digitalCitizenship: { correct: number, total: number, percentage: number }
    }
  },

  // Level 1 - Computer Skills for Everyday Life
  digitalSkillsLevel1: {
    completed: boolean,
    score: number,                    // Points earned (0-40)
    passed: boolean,                  // true if score >= 18
    timeSpent: number,
    completedAt: timestamp,
    responses: [...],
    topicBreakdown: {
      microsoftApps: { correct: number, total: number, percentage: number },
      onlineBanking: { correct: number, total: number, percentage: number },
      cloudStorage: { correct: number, total: number, percentage: number },
      digitalIdentity: { correct: number, total: number, percentage: number },
      internetSkills: { correct: number, total: number, percentage: number }
    }
  },

  // Level 2 - Computer Skills for Work
  digitalSkillsLevel2: {
    completed: boolean,
    score: number,                    // Points earned (0-44)
    passed: boolean,                  // true if score >= 20
    timeSpent: number,
    completedAt: timestamp,
    responses: [...],
    topicBreakdown: {
      advancedFormatting: { correct: number, total: number, percentage: number },
      spreadsheets: { correct: number, total: number, percentage: number },
      presentations: { correct: number, total: number, percentage: number },
      workplaceSkills: { correct: number, total: number, percentage: number },
      collaboration: { correct: number, total: number, percentage: number }
    }
  },

  // Entry Level 3 - Improvers Plus
  digitalSkillsEntryLevel3: {
    completed: boolean,
    score: number,                    // Points earned (0-32)
    passed: boolean,                  // true if score >= 14
    timeSpent: number,
    completedAt: timestamp,
    responses: [...],
    topicBreakdown: {
      operatingSystems: { correct: number, total: number, percentage: number },
      emailProficiency: { correct: number, total: number, percentage: number },
      onlineTransactions: { correct: number, total: number, percentage: number },
      digitalSafety: { correct: number, total: number, percentage: number },
      troubleshooting: { correct: number, total: number, percentage: number }
    }
  },

  // ICDL Level 2
  digitalSkillsICDLLevel2: {
    completed: boolean,
    score: number,                    // Points earned (0-50)
    passed: boolean,                  // true if score >= 22
    timeSpent: number,
    completedAt: timestamp,
    responses: [...],
    topicBreakdown: {
      timedExam: { correct: number, total: number, percentage: number },
      wordAdvanced: { correct: number, total: number, percentage: number },
      excelAdvanced: { correct: number, total: number, percentage: number },
      powerpointAdvanced: { correct: number, total: number, percentage: number },
      employmentSkills: { correct: number, total: number, percentage: number }
    }
  },

  // ICDL Level 3
  digitalSkillsICDLLevel3: {
    completed: boolean,
    score: number,                    // Points earned (0-60)
    passed: boolean,                  // true if score >= 26
    timeSpent: number,
    completedAt: timestamp,
    responses: [...],
    topicBreakdown: {
      advancedMicrosoft: { correct: number, total: number, percentage: number },
      itCareers: { correct: number, total: number, percentage: number },
      higherEducation: { correct: number, total: number, percentage: number },
      professionalSkills: { correct: number, total: number, percentage: number },
      certification: { correct: number, total: number, percentage: number }
    }
  }
}
```

#### Detailed Response Logging (Optional)
For comprehensive analysis and quality assurance:

```javascript
{
  // Detailed responses for each level (optional, for admin review)
  digitalSkillsEntryLevel2DetailedResponses: {
    assessmentMetadata: {
      startTime: timestamp,
      endTime: timestamp,
      userAgent: string,
      ipAddress: string,
      sessionId: string
    },
    questionResponses: [
      {
        questionId: number,
        questionText: string,
        options: [string],
        correctAnswer: string,
        userAnswer: string,
        isCorrect: boolean,
        timeSpent: number,
        timestamp: timestamp,
        interactionLogs: [
          {
            type: string,              // "option_selected", "option_changed", "question_viewed"
            timestamp: timestamp,
            details: object
          }
        ]
      }
    ]
  }
  // Similar structure for other levels...
}
```

## Level Specifications

### Assessment Parameters
```javascript
const digitalSkillsLevels = {
  EntryLevel2: {
    name: "Computer Skills Beginners",
    questionCount: 15,
    timeLimit: 25 * 60,    // 25 minutes
    passingScore: 12,
    maxScore: 30,
    topics: ["computerBasics", "mouseKeyboard", "basicOperations", "fileManagement", "basicSafety"]
  },
  EntryLevel2Plus: {
    name: "Computer Skills Beginners Plus",
    questionCount: 18,
    timeLimit: 30 * 60,    // 30 minutes
    passingScore: 15,
    maxScore: 36,
    topics: ["laptopDesktop", "applications", "internetSafety", "emailBasics", "digitalCitizenship"]
  },
  Level1: {
    name: "Computer Skills for Everyday Life",
    questionCount: 20,
    timeLimit: 35 * 60,    // 35 minutes
    passingScore: 18,
    maxScore: 40,
    topics: ["microsoftApps", "onlineBanking", "cloudStorage", "digitalIdentity", "internetSkills"]
  },
  Level2: {
    name: "Computer Skills for Work",
    questionCount: 22,
    timeLimit: 40 * 60,    // 40 minutes
    passingScore: 20,
    maxScore: 44,
    topics: ["advancedFormatting", "spreadsheets", "presentations", "workplaceSkills", "collaboration"]
  },
  EntryLevel3: {
    name: "Improvers Plus",
    questionCount: 16,
    timeLimit: 30 * 60,    // 30 minutes
    passingScore: 14,
    maxScore: 32,
    topics: ["operatingSystems", "emailProficiency", "onlineTransactions", "digitalSafety", "troubleshooting"]
  },
  ICDLLevel2: {
    name: "ICDL Level 2",
    questionCount: 25,
    timeLimit: 45 * 60,    // 45 minutes
    passingScore: 22,
    maxScore: 50,
    topics: ["timedExam", "wordAdvanced", "excelAdvanced", "powerpointAdvanced", "employmentSkills"]
  },
  ICDLLevel3: {
    name: "ICDL Level 3",
    questionCount: 30,
    timeLimit: 50 * 60,    // 50 minutes
    passingScore: 26,
    maxScore: 60,
    topics: ["advancedMicrosoft", "itCareers", "higherEducation", "professionalSkills", "certification"]
  }
};
```

## Integration with Existing System

### Student Assessment Flow
The digital skills assessment integrates with the existing student assessment flow:

1. **English Assessment** → Student completes English proficiency assessment
2. **Digital Skills Assessment** → Student can optionally take digital skills assessment
3. **Mathematics Assessment** → Student can optionally take mathematics assessment

### Database Compatibility
- Uses same company/user document structure as existing assessments
- Compatible with existing admin dashboard queries
- Maintains consistency with English and Mathematics assessment patterns

### Document Size Considerations
- Basic digital skills assessment fields add approximately 3-5KB per user document
- Comprehensive response logging adds approximately 8-20KB per user document
- Total digital skills assessment data: 11-25KB per user document
- Well within Firestore document size limits (1MB)

### Query Optimization
```javascript
// Efficient query for digital skills assessment status
const query = userRef.where('digitalSkillsAssessmentCompleted', '==', true)
                    .where('digitalSkillsHighestLevelCompleted', '==', 'ICDLLevel3');
```

## Security and Privacy

### Data Protection
- All assessment data encrypted in transit and at rest
- User responses stored securely with proper access controls
- Detailed response logs available only to authorized administrators

### Access Control
- Students can only access their own assessment data
- Administrators can access aggregated analytics
- Individual detailed responses require elevated permissions

This schema provides comprehensive tracking of digital skills competency across all 7 levels while maintaining compatibility with the existing assessment platform architecture.
