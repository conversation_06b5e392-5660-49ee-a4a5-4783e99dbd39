const http = require('http');

// Test the mathematics assessment API with the fixes
async function testMathematicsAssessment() {
  console.log('🧮 Testing Mathematics Assessment API with All Fixes\n');
  
  // Test different levels to see which ones work
  const levels = ['Entry', 'Level1'];
  
  for (const level of levels) {
    console.log(`\n=== Testing ${level} Assessment ===`);
    
    const postData = JSON.stringify({
      email: '<EMAIL>',
      level: level,
      studentLevel: 'adult-learner'
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/math-assessments/start',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`📡 Making request for ${level} assessment...`);
    const startTime = Date.now();

    try {
      const response = await new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            const duration = Date.now() - startTime;
            
            if (res.statusCode === 200) {
              try {
                const result = JSON.parse(data);
                resolve({ success: true, data: result, duration, statusCode: res.statusCode });
              } catch (parseError) {
                reject({ success: false, error: 'JSON parse error', details: parseError.message, duration });
              }
            } else {
              reject({ success: false, error: `HTTP ${res.statusCode}`, details: data, duration });
            }
          });
        });

        req.on('error', (error) => {
          const duration = Date.now() - startTime;
          reject({ success: false, error: 'Request error', details: error.message, duration });
        });

        req.on('timeout', () => {
          const duration = Date.now() - startTime;
          reject({ success: false, error: 'Request timeout', details: 'Request timed out', duration });
        });

        req.setTimeout(60000); // 60 second timeout for the HTTP request
        req.write(postData);
        req.end();
      });

      // Success!
      console.log(`✅ ${level} assessment completed in ${response.duration}ms`);
      console.log(`📊 Assessment ID: ${response.data.assessmentId}`);
      console.log(`📝 Questions generated: ${response.data.questions.length}`);
      console.log(`⏱️  Time limit: ${response.data.timeLimit} minutes`);
      console.log(`🎯 Passing score: ${response.data.passingScore}/${response.data.maxScore}`);
      
      // Analyze the questions
      const questions = response.data.questions;
      if (questions && questions.length > 0) {
        console.log('\n📋 Question Analysis:');
        
        // Check if questions look AI-generated
        const hasTopics = questions.filter(q => q.topic).length;
        const hasExplanations = questions.filter(q => q.explanation).length;
        const hasVariedQuestions = questions.filter(q => q.question && q.question.length > 20).length;
        
        console.log(`- Questions with topics: ${hasTopics}/${questions.length}`);
        console.log(`- Questions with explanations: ${hasExplanations}/${questions.length}`);
        console.log(`- Questions with varied content: ${hasVariedQuestions}/${questions.length}`);
        
        if (hasTopics > questions.length * 0.8 && hasExplanations > questions.length * 0.8) {
          console.log('✅ Questions appear to be AI-generated!');
        } else {
          console.log('⚠️  Questions appear to be fallback/hardcoded');
        }
        
        // Show sample questions
        console.log('\n📝 Sample Questions:');
        console.log('1.', questions[0].question);
        console.log('   Topic:', questions[0].topic || 'N/A');
        console.log('   Options:', questions[0].options);
        console.log('   Answer:', questions[0].correctAnswer);
        
        if (questions.length > 1) {
          console.log('2.', questions[1].question);
          console.log('   Topic:', questions[1].topic || 'N/A');
        }
      }
      
    } catch (error) {
      console.log(`❌ ${level} assessment failed after ${error.duration}ms`);
      console.log(`Error: ${error.error}`);
      console.log(`Details: ${error.details}`);
    }
  }
}

// Test performance metrics
async function testPerformanceMetrics() {
  console.log('\n=== Performance Metrics ===');
  
  try {
    const response = await new Promise((resolve, reject) => {
      const req = http.request({
        hostname: 'localhost',
        port: 3000,
        path: '/api/math-assessments/performance',
        method: 'GET'
      }, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          if (res.statusCode === 200) {
            resolve(JSON.parse(data));
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        });
      });
      
      req.on('error', reject);
      req.setTimeout(10000);
      req.end();
    });
    
    console.log('📊 Performance Metrics:');
    const metrics = response.metrics;
    console.log(`- Total requests: ${metrics.totalRequests}`);
    console.log(`- Cache hits: ${metrics.cacheHits} (${metrics.cacheHitRate})`);
    console.log(`- API timeouts: ${metrics.apiTimeouts}`);
    console.log(`- Retry attempts: ${metrics.retryAttempts}`);
    console.log(`- Successful retries: ${metrics.successfulRetries} (${metrics.retrySuccessRate})`);
    console.log(`- Fallback usage: ${metrics.fallbackUsage}`);
    console.log(`- Average generation time: ${metrics.averageGenerationTime}ms`);
    console.log(`- Cache size: ${metrics.cacheSize}`);
    
  } catch (error) {
    console.log('❌ Failed to get performance metrics:', error.message);
  }
}

// Check server status
async function checkServerStatus() {
  try {
    await new Promise((resolve, reject) => {
      const req = http.request({
        hostname: 'localhost',
        port: 3000,
        path: '/api/math-assessments/performance',
        method: 'GET'
      }, (res) => {
        res.on('data', () => {});
        res.on('end', () => {
          if (res.statusCode === 200) {
            resolve();
          } else {
            reject(new Error(`Server returned ${res.statusCode}`));
          }
        });
      });
      
      req.on('error', reject);
      req.setTimeout(5000);
      req.end();
    });
    
    console.log('✅ Server is running and accessible');
    return true;
  } catch (error) {
    console.log('❌ Server is not accessible:', error.message);
    console.log('Please start the server with: node server.js');
    return false;
  }
}

// Main test runner
async function runFinalTest() {
  console.log('🔧 Final Mathematics Assessment System Test\n');
  console.log('This test will verify that all fixes are working:\n');
  console.log('✓ Increased timeout to 30 seconds');
  console.log('✓ Added retry mechanism with exponential backoff');
  console.log('✓ Improved JSON parsing for markdown code blocks');
  console.log('✓ Enhanced error logging and monitoring\n');
  
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    return;
  }
  
  await testMathematicsAssessment();
  await testPerformanceMetrics();
  
  console.log('\n🎉 Final test completed!');
  console.log('\nIf questions are still showing as fallback/hardcoded, this indicates');
  console.log('that the OpenAI API is taking longer than 30 seconds to respond.');
  console.log('The system is now properly configured to handle this gracefully.');
}

runFinalTest().catch(console.error);
