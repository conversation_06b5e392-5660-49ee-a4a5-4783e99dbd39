/**
 * Test script to validate fallback question deduplication
 * Specifically focuses on Entry level questions to ensure no duplicates
 */

// Mock the question deduplication functions
function createQuestionSignature(question) {
  const normalizedQuestion = question.question
    .toLowerCase()
    .replace(/\d+/g, 'NUM')
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
  
  return `${question.topic}_${question.type}_${normalizedQuestion}`;
}

function normalizeQuestionText(text) {
  return text
    .toLowerCase()
    .replace(/\d+(\.\d+)?/g, 'X')
    .replace(/[£$€¥]/g, 'CURRENCY')
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

function calculateTextSimilarity(text1, text2) {
  const words1 = new Set(text1.split(' '));
  const words2 = new Set(text2.split(' '));
  
  const intersection = new Set([...words1].filter(word => words2.has(word)));
  const union = new Set([...words1, ...words2]);
  
  return intersection.size / union.size;
}

function areQuestionsSimilar(question1, question2) {
  if (question1.topic === question2.topic && question1.type === question2.type) {
    const text1 = normalizeQuestionText(question1.question);
    const text2 = normalizeQuestionText(question2.question);
    
    const similarity = calculateTextSimilarity(text1, text2);
    return similarity > 0.7;
  }
  return false;
}

function ensureQuestionDiversity(questions, level) {
  console.log(`🔍 Analyzing question diversity for ${level} level...`);
  
  const uniqueQuestions = [];
  const seenQuestions = new Set();
  const topicCounts = {};
  const typeCounts = {};
  
  let duplicatesRemoved = 0;
  let similarQuestionsFound = 0;
  
  for (const question of questions) {
    const signature = createQuestionSignature(question);
    
    if (seenQuestions.has(signature)) {
      duplicatesRemoved++;
      console.log(`🚫 Removed duplicate question: "${question.question.substring(0, 50)}..."`);
      continue;
    }
    
    const isSimilar = uniqueQuestions.some(existing => 
      areQuestionsSimilar(question, existing)
    );
    
    if (isSimilar) {
      similarQuestionsFound++;
      console.log(`⚠️ Found similar question: "${question.question.substring(0, 50)}..."`);
      continue;
    }
    
    uniqueQuestions.push(question);
    seenQuestions.add(signature);
    topicCounts[question.topic] = (topicCounts[question.topic] || 0) + 1;
    typeCounts[question.type] = (typeCounts[question.type] || 0) + 1;
  }
  
  console.log(`📊 Question Diversity Analysis:`);
  console.log(`  Original questions: ${questions.length}`);
  console.log(`  Unique questions: ${uniqueQuestions.length}`);
  console.log(`  Duplicates removed: ${duplicatesRemoved}`);
  console.log(`  Similar questions found: ${similarQuestionsFound}`);
  console.log(`  Topic distribution:`, topicCounts);
  console.log(`  Type distribution:`, typeCounts);
  
  return uniqueQuestions;
}

// Mock Entry level fallback questions (expanded set)
const entryLevelFallbackQuestions = [
  // Arithmetic Questions (7 questions)
  {
    id: 1,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 25 + 37?",
    options: ["52", "62", "72", "82"],
    correctAnswer: "62",
    points: 2
  },
  {
    id: 2,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 84 - 29?",
    options: ["45", "55", "65", "75"],
    correctAnswer: "55",
    points: 2
  },
  {
    id: 3,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 8 × 7?",
    options: ["54", "56", "58", "64"],
    correctAnswer: "56",
    points: 2
  },
  {
    id: 4,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 72 ÷ 9?",
    options: ["6", "7", "8", "9"],
    correctAnswer: "8",
    points: 2
  },
  {
    id: 5,
    type: "numeric",
    topic: "arithmetic",
    question: "Calculate 15 + 28 + 17",
    correctAnswer: "60",
    points: 2
  },
  {
    id: 6,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 6 × 9?",
    options: ["52", "54", "56", "58"],
    correctAnswer: "54",
    points: 2
  },
  {
    id: 7,
    type: "numeric",
    topic: "arithmetic",
    question: "What is 100 - 47?",
    correctAnswer: "53",
    points: 2
  },
  
  // Fractions Questions (4 questions)
  {
    id: 8,
    type: "numeric",
    topic: "fractions",
    question: "What is 1/2 of 24?",
    correctAnswer: "12",
    points: 2
  },
  {
    id: 9,
    type: "multiple-choice",
    topic: "fractions",
    question: "What is 1/4 of 20?",
    options: ["4", "5", "6", "8"],
    correctAnswer: "5",
    points: 2
  },
  {
    id: 10,
    type: "multiple-choice",
    topic: "fractions",
    question: "Which fraction is equivalent to 0.5?",
    options: ["1/4", "1/2", "1/3", "2/3"],
    correctAnswer: "1/2",
    points: 2
  },
  {
    id: 11,
    type: "numeric",
    topic: "fractions",
    question: "What is 3/4 of 20?",
    correctAnswer: "15",
    points: 2
  },
  
  // Percentages Questions (3 questions)
  {
    id: 12,
    type: "multiple-choice",
    topic: "percentages",
    question: "What is 10% of 50?",
    options: ["3", "5", "10", "15"],
    correctAnswer: "5",
    points: 2
  },
  {
    id: 13,
    type: "multiple-choice",
    topic: "percentages",
    question: "What is 25% of 60?",
    options: ["10", "15", "20", "25"],
    correctAnswer: "15",
    points: 2
  },
  {
    id: 14,
    type: "numeric",
    topic: "percentages",
    question: "What is 50% of 60?",
    correctAnswer: "30",
    points: 2
  },
  
  // Measurement Questions (3 questions)
  {
    id: 15,
    type: "multiple-choice",
    topic: "measurement",
    question: "How many centimetres are in 2.5 metres?",
    options: ["25", "250", "2500", "25000"],
    correctAnswer: "250",
    points: 2
  },
  {
    id: 16,
    type: "multiple-choice",
    topic: "measurement",
    question: "How many grams are in 1.5 kilograms?",
    options: ["150", "1500", "15000", "150000"],
    correctAnswer: "1500",
    points: 2
  },
  {
    id: 17,
    type: "numeric",
    topic: "measurement",
    question: "How many minutes are in 2.5 hours?",
    correctAnswer: "150",
    points: 2
  },
  
  // Basic Algebra Questions (2 questions)
  {
    id: 18,
    type: "numeric",
    topic: "basicAlgebra",
    question: "If x + 5 = 12, what is x?",
    correctAnswer: "7",
    points: 2
  },
  {
    id: 19,
    type: "multiple-choice",
    topic: "basicAlgebra",
    question: "If y - 3 = 8, what is y?",
    options: ["5", "8", "11", "24"],
    correctAnswer: "11",
    points: 2
  },
  
  // Data Handling Questions (3 questions)
  {
    id: 20,
    type: "multiple-choice",
    topic: "dataHandling",
    question: "What is the average of 4, 6, and 8?",
    options: ["5", "6", "7", "8"],
    correctAnswer: "6",
    points: 2
  },
  {
    id: 21,
    type: "numeric",
    topic: "dataHandling",
    question: "What is the sum of 12, 15, and 18?",
    correctAnswer: "45",
    points: 2
  },
  {
    id: 22,
    type: "multiple-choice",
    topic: "dataHandling",
    question: "In the numbers 3, 7, 5, 9, 5, what is the mode?",
    options: ["3", "5", "7", "9"],
    correctAnswer: "5",
    points: 2
  }
];

// Test function to simulate the old repetitive behavior
function simulateOldFallbackBehavior(questions, requiredCount) {
  const result = [];
  for (let i = 0; i < requiredCount; i++) {
    const questionIndex = i % questions.length;
    const question = { ...questions[questionIndex] };
    question.id = i + 1;
    result.push(question);
  }
  return result;
}

// Run comprehensive tests
function runFallbackDeduplicationTests() {
  console.log('🧪 Testing Fallback Question Deduplication');
  console.log('==========================================\n');
  
  const requiredCount = 22; // Entry level needs 22 questions
  
  // Test 1: Check if we have enough unique base questions
  console.log('📝 Test 1: Base Question Coverage');
  console.log('='.repeat(35));
  
  console.log(`Entry level requires: ${requiredCount} questions`);
  console.log(`Base questions available: ${entryLevelFallbackQuestions.length} questions`);
  console.log(`Coverage: ${entryLevelFallbackQuestions.length >= requiredCount ? '✅ SUFFICIENT' : '⚠️ INSUFFICIENT'}`);
  
  // Test 2: Simulate old behavior (with repetition)
  console.log('\n📝 Test 2: Old Behavior Simulation (With Repetition)');
  console.log('='.repeat(55));
  
  const oldBehaviorQuestions = simulateOldFallbackBehavior(entryLevelFallbackQuestions.slice(0, 5), requiredCount);
  const oldUniqueQuestions = ensureQuestionDiversity(oldBehaviorQuestions, 'Entry (Old)');
  
  console.log(`\n📊 Old Behavior Results:`);
  console.log(`  Generated questions: ${oldBehaviorQuestions.length}`);
  console.log(`  Unique questions: ${oldUniqueQuestions.length}`);
  console.log(`  Duplicates: ${oldBehaviorQuestions.length - oldUniqueQuestions.length}`);
  console.log(`  Duplication rate: ${Math.round(((oldBehaviorQuestions.length - oldUniqueQuestions.length) / oldBehaviorQuestions.length) * 100)}%`);
  
  // Test 3: New behavior (with full question set and deduplication)
  console.log('\n📝 Test 3: New Behavior (With Full Question Set)');
  console.log('='.repeat(50));
  
  const newBehaviorQuestions = entryLevelFallbackQuestions.slice(0, requiredCount);
  const newUniqueQuestions = ensureQuestionDiversity(newBehaviorQuestions, 'Entry (New)');
  
  console.log(`\n📊 New Behavior Results:`);
  console.log(`  Generated questions: ${newBehaviorQuestions.length}`);
  console.log(`  Unique questions: ${newUniqueQuestions.length}`);
  console.log(`  Duplicates: ${newBehaviorQuestions.length - newUniqueQuestions.length}`);
  console.log(`  Duplication rate: ${Math.round(((newBehaviorQuestions.length - newUniqueQuestions.length) / newBehaviorQuestions.length) * 100)}%`);
  
  // Test 4: Topic distribution analysis
  console.log('\n📝 Test 4: Topic Distribution Analysis');
  console.log('='.repeat(40));
  
  const topicCounts = {};
  newUniqueQuestions.forEach(q => {
    topicCounts[q.topic] = (topicCounts[q.topic] || 0) + 1;
  });
  
  console.log('Topic distribution in final question set:');
  Object.entries(topicCounts).forEach(([topic, count]) => {
    const percentage = Math.round((count / newUniqueQuestions.length) * 100);
    console.log(`  ${topic}: ${count} questions (${percentage}%)`);
  });
  
  // Test 5: Question type distribution
  console.log('\n📝 Test 5: Question Type Distribution');
  console.log('='.repeat(40));
  
  const typeCounts = {};
  newUniqueQuestions.forEach(q => {
    typeCounts[q.type] = (typeCounts[q.type] || 0) + 1;
  });
  
  console.log('Question type distribution:');
  Object.entries(typeCounts).forEach(([type, count]) => {
    const percentage = Math.round((count / newUniqueQuestions.length) * 100);
    console.log(`  ${type}: ${count} questions (${percentage}%)`);
  });
  
  // Final summary
  console.log('\n📋 Final Test Summary');
  console.log('=====================');
  console.log(`✅ Base question coverage: ${entryLevelFallbackQuestions.length >= requiredCount ? 'SUFFICIENT' : 'NEEDS MORE'}`);
  console.log(`✅ Deduplication working: ${newUniqueQuestions.length === newBehaviorQuestions.length ? 'YES' : 'NO'}`);
  console.log(`✅ No repetition needed: ${newUniqueQuestions.length >= requiredCount ? 'YES' : 'NO'}`);
  console.log(`✅ Topic variety: ${Object.keys(topicCounts).length >= 5 ? 'GOOD' : 'NEEDS IMPROVEMENT'}`);
  console.log(`✅ Question type variety: ${Object.keys(typeCounts).length >= 2 ? 'GOOD' : 'NEEDS IMPROVEMENT'}`);
  
  const improvementPercentage = Math.round(((newUniqueQuestions.length - oldUniqueQuestions.length) / oldUniqueQuestions.length) * 100);
  console.log(`\n🎯 Overall Improvement: ${improvementPercentage}% more unique questions`);
  
  console.log('\n🎉 Fallback question deduplication testing completed!');
}

// Run the tests
runFallbackDeduplicationTests();
