/* ============================================================================ */
/* INTERACTIVE MATHEMATICS QUESTION STYLES */
/* ============================================================================ */

/* Base Interactive Question Styles */
.interactive-question {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 1.5rem 0;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.interactive-question:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
}

.interactive-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.reset-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.reset-btn:hover {
  background: #5a6268;
}

.btn-icon {
  font-size: 1.1rem;
}

/* ============================================================================ */
/* EQUATION BUILDER STYLES */
/* ============================================================================ */

.equation-builder-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  min-height: 200px;
}

.equation-components {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  padding: 1rem;
  background: #ffffff;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  min-height: 60px;
}

.equation-component {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  background: #007bff;
  color: white;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: grab;
  user-select: none;
  transition: all 0.2s ease;
  padding: 0 0.75rem;
  border: 2px solid #0056b3;
}

.equation-component:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.equation-component:active {
  cursor: grabbing;
  transform: scale(0.95);
}

.equation-workspace {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1.5rem;
  background: #ffffff;
  border: 2px solid #28a745;
  border-radius: 8px;
  min-height: 80px;
  align-items: center;
  justify-content: center;
}

.equation-drop-zone {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  height: 50px;
  border: 2px dashed #343a40;
  border-radius: 6px;
  background: #f8f9fa;
  font-size: 1.2rem;
  font-weight: 600;
  color: #343a40;
  transition: all 0.2s ease;
  margin: 0 2px;
}

.equation-drop-zone.drag-over {
  border-color: #007bff;
  background: #e3f2fd;
  transform: scale(1.05);
}

.equation-drop-zone.filled {
  background: #007bff;
  color: white;
  border-color: #007bff;
  border-style: solid;
}

/* ============================================================================ */
/* GRAPH PLOTTING STYLES */
/* ============================================================================ */

.graph-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.graph-canvas {
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: #ffffff;
  cursor: crosshair;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.graph-canvas:hover {
  border-color: #007bff;
}

.graph-coordinates {
  padding: 0.5rem 1rem;
  background: #e9ecef;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #495057;
  min-width: 200px;
  text-align: center;
}

/* ============================================================================ */
/* SORTING ACTIVITY STYLES */
/* ============================================================================ */

.sorting-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  min-height: 300px;
}

.sorting-items {
  background: #ffffff;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.sorting-items::before {
  content: "Items to Sort";
  font-weight: 600;
  color: #212529;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

.sorting-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border: 2px solid #343a40;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
  font-weight: 500;
  color: #212529;
}

.sorting-item:hover {
  background: #e9ecef;
  border-color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sorting-item:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.sorting-item img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
}

.sorting-categories {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sorting-category {
  background: #ffffff;
  border: 2px solid #28a745;
  border-radius: 8px;
  overflow: hidden;
}

.category-label {
  background: #28a745;
  color: white;
  padding: 0.75rem;
  font-weight: 600;
  text-align: center;
}

.category-drop-zone {
  min-height: 100px;
  padding: 1rem;
  border: 2px dashed #28a745;
  border-top: none;
  background: #f8fff9;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.category-drop-zone.drag-over {
  background: #d4edda;
  border-style: solid;
  transform: scale(1.02);
}

.category-drop-zone .sorting-item {
  margin: 0;
  background: #d1ecf1;
  border-color: #bee5eb;
}

/* ============================================================================ */
/* ENHANCED NUMBER LINE STYLES */
/* ============================================================================ */

.number-line-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 2rem;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.number-line-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 0.9rem;
  color: #495057;
  font-weight: 500;
}

.number-line-track {
  position: relative;
  height: 8px;
  background: linear-gradient(to right, #007bff 0%, #28a745 100%);
  border-radius: 4px;
  margin: 10px 20px;
  cursor: pointer;
}

.number-line-handle {
  position: absolute;
  top: -12px;
  width: 32px;
  height: 32px;
  background: #ffffff;
  border: 3px solid #007bff;
  border-radius: 50%;
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.number-line-handle:hover {
  border-color: #0056b3;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.number-line-handle:focus {
  outline: none;
  border-color: #0056b3;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.number-line-handle:active {
  cursor: grabbing;
  transform: scale(0.95);
}

.number-line-value {
  text-align: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: #007bff;
  background: #e3f2fd;
  padding: 0.75rem;
  border-radius: 6px;
  border: 2px solid #bbdefb;
}

/* ============================================================================ */
/* ENHANCED DRAG AND DROP STYLES */
/* ============================================================================ */

.matching-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  min-height: 250px;
}

.draggable-items {
  background: #ffffff;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.draggable-items::before {
  content: "Drag These";
  font-weight: 600;
  color: #212529;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

.draggable-item {
  padding: 0.75rem;
  background: #f8f9fa;
  border: 2px solid #343a40;
  border-radius: 6px;
  cursor: grab;
  text-align: center;
  font-weight: 500;
  transition: all 0.2s ease;
  color: #212529;
}

.draggable-item:hover {
  background: #e9ecef;
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

.draggable-item:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.drop-zones {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.drop-zone {
  min-height: 60px;
  padding: 1rem;
  background: #f8fff9;
  border: 2px dashed #28a745;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: #212529;
  transition: all 0.2s ease;
  position: relative;
}

.drop-zone::before {
  content: attr(data-label);
  position: absolute;
  top: -12px;
  left: 10px;
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.drop-zone.drag-over {
  background: #d4edda;
  border-style: solid;
  transform: scale(1.02);
}

.drop-zone .draggable-item {
  margin: 0;
  background: #d1ecf1;
  border-color: #bee5eb;
}

/* ============================================================================ */
/* ENHANCED AREA MODEL STYLES */
/* ============================================================================ */

.area-model-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  background: #ffffff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.fraction-bars {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.fraction-bar {
  display: flex;
  height: 60px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;
}

.fraction-segment {
  border-right: 1px solid #dee2e6;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #212529;
}

.fraction-segment:last-child {
  border-right: none;
}

.fraction-segment:hover {
  background: #e3f2fd;
}

.fraction-segment.shaded {
  background: #007bff;
  color: white;
}

.geometric-shapes {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.geometric-shape {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  background: #ffffff;
}

.geometric-shape:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.geometric-shape.shaded {
  background: #e3f2fd;
  border-color: #007bff;
}

.geometric-shape svg {
  display: block;
}

.geometric-shape rect,
.geometric-shape circle,
.geometric-shape polygon {
  fill: transparent;
  stroke: #212529;
  stroke-width: 2;
  transition: all 0.2s ease;
}

.geometric-shape.shaded rect,
.geometric-shape.shaded circle,
.geometric-shape.shaded polygon {
  fill: #007bff;
  fill-opacity: 0.3;
  stroke: #0056b3;
}

/* ============================================================================ */
/* RESPONSIVE DESIGN */
/* ============================================================================ */

@media (max-width: 768px) {
  .interactive-question {
    padding: 1.5rem;
  }

  .sorting-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .matching-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .equation-builder-container {
    gap: 1.5rem;
  }

  .equation-components,
  .equation-workspace {
    padding: 1rem;
  }

  .graph-canvas {
    width: 100%;
    max-width: 350px;
    height: auto;
  }

  .number-line-container {
    padding: 1.5rem;
  }

  .area-model-container {
    padding: 1.5rem;
  }

  .fraction-bar {
    height: 50px;
  }

  .geometric-shapes {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .interactive-question {
    padding: 1rem;
    margin: 1rem 0;
  }

  .interactive-controls {
    flex-direction: column;
    align-items: center;
  }

  .equation-components {
    gap: 0.5rem;
  }

  .equation-component {
    min-width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .sorting-item {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .sorting-item img {
    width: 24px;
    height: 24px;
  }

  .number-line-handle {
    width: 28px;
    height: 28px;
    top: -10px;
  }
}

/* ============================================================================ */
/* ACCESSIBILITY ENHANCEMENTS */
/* ============================================================================ */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .interactive-question {
    border-width: 3px;
  }

  .equation-component,
  .sorting-item,
  .draggable-item {
    border-width: 3px;
  }

  .number-line-handle {
    border-width: 4px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .interactive-question,
  .equation-component,
  .sorting-item,
  .draggable-item,
  .number-line-handle,
  .fraction-segment,
  .geometric-shape {
    transition: none;
  }

  .equation-component:hover,
  .sorting-item:hover,
  .draggable-item:hover,
  .number-line-handle:hover,
  .geometric-shape:hover {
    transform: none;
  }
}

/* Focus indicators for keyboard navigation */
.equation-component:focus,
.sorting-item:focus,
.draggable-item:focus,
.drop-zone:focus,
.fraction-segment:focus,
.geometric-shape:focus {
  outline: 3px solid #007bff;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
