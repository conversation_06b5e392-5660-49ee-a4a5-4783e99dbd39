const fetch = require('node-fetch');

async function testDigitalSkillsEndpoint() {
  try {
    console.log('Testing digital skills assessment endpoint...');
    
    const response = await fetch('http://localhost:3003/api/digital-skills-assessments/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        level: 'EntryLevel2',
        email: '<EMAIL>',
        studentLevel: 'adult-learner'
      }),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());

    if (response.ok) {
      const data = await response.json();
      console.log('Success! Response data:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testDigitalSkillsEndpoint();
