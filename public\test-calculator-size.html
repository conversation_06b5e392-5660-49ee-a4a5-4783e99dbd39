<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculator Size Test</title>
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .viewport-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Calculator Size Test</h1>
        <div class="viewport-info">
            <strong>Current viewport:</strong> <span id="viewport-size"></span><br>
            <strong>Calculator container max-width:</strong> 350px (desktop), 100% (mobile)
        </div>
        
        <div class="interactive-question">
            <h3>Calculate 84 - 37 step by step</h3>
            <div id="visual-calculator">
                <div class="calculator-container">
                    <div class="calculator-display">
                        <div class="calculation-steps" id="calculation-steps">
                            Step 1: Enter 84<br>
                            Step 2: Press - (minus)<br>
                            Step 3: Enter 37
                        </div>
                        <div class="current-display" id="current-display">84 - 37</div>
                    </div>
                    <div class="calculator-keypad" id="calculator-keypad">
                        <button class="calc-btn clear">C</button>
                        <button class="calc-btn">±</button>
                        <button class="calc-btn">%</button>
                        <button class="calc-btn operator">÷</button>
                        
                        <button class="calc-btn">7</button>
                        <button class="calc-btn">8</button>
                        <button class="calc-btn">9</button>
                        <button class="calc-btn operator">×</button>
                        
                        <button class="calc-btn">4</button>
                        <button class="calc-btn">5</button>
                        <button class="calc-btn">6</button>
                        <button class="calc-btn operator">-</button>
                        
                        <button class="calc-btn">1</button>
                        <button class="calc-btn">2</button>
                        <button class="calc-btn">3</button>
                        <button class="calc-btn operator">+</button>
                        
                        <button class="calc-btn" style="grid-column: span 2;">0</button>
                        <button class="calc-btn">.</button>
                        <button class="calc-btn equals" style="grid-column: span 2;">=</button>
                    </div>
                    <div class="calculator-controls">
                        <button class="reset-btn" type="button">
                            <span class="btn-icon">C</span>
                            <span class="btn-text">Clear</span>
                        </button>
                        <button class="step-btn" type="button">
                            <span class="btn-icon">→</span>
                            <span class="btn-text">Next Step</span>
                        </button>
                    </div>
                </div>
                <p class="input-hint">Use the calculator to solve the problem step by step</p>
            </div>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            document.getElementById('viewport-size').textContent = `${width}px × ${height}px`;
        }
        
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
        
        // Add click handlers to buttons for testing
        document.querySelectorAll('.calc-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('Button clicked:', this.textContent);
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            });
        });
    </script>
</body>
</html>
