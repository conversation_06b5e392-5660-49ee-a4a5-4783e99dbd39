/**
 * Interactive Question Data Library
 * Pre-loaded static interactive question data for all 8 question types
 * Organized by assessment levels: Entry, Level1, GCSEPart1, GCSEPart2
 */

class InteractiveQuestionData {
  constructor() {
    this.questionData = {
      Entry: {
        'number-line': [
          {
            id: 'entry_nl_1',
            question: 'Place the number 7 on the number line',
            topic: 'Number Recognition',
            type: 'number-line',
            config: {
              min: 0,
              max: 10,
              step: 1,
              snapToGrid: true,
              correctAnswer: 7
            },
            points: 2
          },
          {
            id: 'entry_nl_2',
            question: 'Show where 2.5 belongs on this number line',
            topic: 'Decimals',
            type: 'number-line',
            config: {
              min: 0,
              max: 5,
              step: 0.5,
              snapToGrid: true,
              correctAnswer: 2.5
            },
            points: 2
          }
        ],
        'drag-drop': [
          {
            id: 'entry_dd_1',
            question: 'Match each fraction to its decimal equivalent',
            topic: 'Fractions and Decimals',
            type: 'drag-drop',
            config: {
              items: [
                { id: 'frac_half', text: '1/2' },
                { id: 'frac_quarter', text: '1/4' },
                { id: 'frac_three_quarters', text: '3/4' }
              ],
              zones: [
                { id: 'dec_half', label: '0.5' },
                { id: 'dec_quarter', label: '0.25' },
                { id: 'dec_three_quarters', label: '0.75' }
              ],
              correctMatches: {
                'dec_half': 'frac_half',
                'dec_quarter': 'frac_quarter',
                'dec_three_quarters': 'frac_three_quarters'
              }
            },
            points: 3
          }
        ],
        'visual-calculator': [
          {
            id: 'entry_vc_1',
            question: 'Calculate 15 + 28 using the calculator',
            topic: 'Addition',
            type: 'visual-calculator',
            config: {
              calculationType: 'basic',
              targetCalculation: '15 + 28',
              expectedResult: 43,
              allowedOperations: ['+', '-', '×', '÷'],
              showSteps: true,
              hint: 'Enter 15, then +, then 28, then ='
            },
            points: 2
          },
          {
            id: 'entry_vc_2',
            question: 'Calculate 84 - 37 step by step',
            topic: 'Subtraction',
            type: 'visual-calculator',
            config: {
              calculationType: 'basic',
              targetCalculation: '84 - 37',
              expectedResult: 47,
              allowedOperations: ['+', '-', '×', '÷'],
              showSteps: true,
              hint: 'Enter 84, then -, then 37, then ='
            },
            points: 2
          }
        ],
        'step-by-step': [
          {
            id: 'entry_sbs_1',
            question: 'Calculate 15 + 28 step by step',
            topic: 'Addition',
            type: 'step-by-step',
            config: {
              steps: [
                {
                  instruction: 'Add the ones: 5 + 8 = ?',
                  expectedAnswer: '13',
                  hint: 'Count on your fingers if needed'
                },
                {
                  instruction: 'Write down 3, carry 1',
                  expectedAnswer: '3',
                  hint: 'The 3 goes in the ones place'
                },
                {
                  instruction: 'Add the tens: 1 + 2 + 1 = ?',
                  expectedAnswer: '4',
                  hint: 'Don\'t forget the carried 1'
                }
              ],
              finalAnswer: '43'
            },
            points: 3
          },
          {
            id: 'entry_sbs_2',
            question: 'Calculate 25% of 80 step by step',
            topic: 'Percentages',
            type: 'step-by-step',
            config: {
              steps: [
                {
                  instruction: 'Convert 25% to a decimal: 25% = ?',
                  expectedAnswer: '0.25',
                  hint: 'Divide by 100'
                },
                {
                  instruction: 'Multiply: 0.25 × 80 = ?',
                  expectedAnswer: '20',
                  hint: 'Think of 25% as 1/4'
                }
              ],
              finalAnswer: '20'
            },
            points: 2
          }
        ],
        'ratio-slider': [
          {
            id: 'entry_rs_1',
            question: 'Show the ratio of red to blue balls if there are 2 red and 3 blue',
            topic: 'Basic Ratios',
            type: 'ratio-slider',
            config: {
              sliders: [
                { id: 'red', label: 'Red balls', min: 1, max: 5, step: 1 },
                { id: 'blue', label: 'Blue balls', min: 1, max: 5, step: 1 }
              ],
              correctRatio: { red: 2, blue: 3 },
              tolerance: 0
            },
            points: 2
          }
        ],
        'pattern-completion': [
          {
            id: 'entry_pc_1',
            question: 'Complete the number pattern: 2, 4, 6, 8, ?',
            topic: 'Number Patterns',
            type: 'pattern-completion',
            config: {
              patternType: 'arithmetic',
              sequence: [2, 4, 6, 8],
              options: [10, 12, 9, 16],
              correctAnswer: 10,
              rule: 'Add 2 each time',
              hint: 'Count by 2s'
            },
            points: 2
          },
          {
            id: 'entry_pc_2',
            question: 'What shape comes next: ○, △, □, ○, △, ?',
            topic: 'Shape Patterns',
            type: 'pattern-completion',
            config: {
              patternType: 'visual',
              sequence: ['circle', 'triangle', 'square', 'circle', 'triangle'],
              options: ['square', 'circle', 'triangle', 'star'],
              correctAnswer: 'square',
              rule: 'Repeating pattern of 3 shapes',
              hint: 'The pattern repeats every 3 shapes'
            },
            points: 2
          }
        ]
      },
      Level1: {
        'number-bonds': [
          {
            id: 'level1_nb_1',
            question: 'Complete the number bond: 8 + ? = 15',
            topic: 'Number Bonds',
            type: 'number-bonds',
            config: {
              bondType: 'addition',
              targetNumber: 15,
              knownNumber: 8,
              missingNumber: 7,
              visualStyle: 'circles',
              showHints: true,
              hint: 'What number do you add to 8 to make 15?'
            },
            points: 2
          },
          {
            id: 'level1_nb_2',
            question: 'Find the missing numbers: 12 = ? + ?',
            topic: 'Number Bonds',
            type: 'number-bonds',
            config: {
              bondType: 'decomposition',
              targetNumber: 12,
              possiblePairs: [
                { a: 6, b: 6 }, { a: 5, b: 7 }, { a: 4, b: 8 }, { a: 3, b: 9 }
              ],
              correctPairs: [{ a: 6, b: 6 }, { a: 5, b: 7 }, { a: 4, b: 8 }, { a: 3, b: 9 }],
              visualStyle: 'blocks',
              allowMultiple: true,
              hint: 'Find different ways to make 12 using two numbers'
            },
            points: 3
          },
          {
            id: 'level1_nb_3',
            question: 'Complete the subtraction bond: 20 - ? = 13',
            topic: 'Number Bonds',
            type: 'number-bonds',
            config: {
              bondType: 'subtraction',
              targetNumber: 13,
              startNumber: 20,
              missingNumber: 7,
              visualStyle: 'number-line',
              showHints: true,
              hint: 'What number do you subtract from 20 to get 13?'
            },
            points: 2
          }
        ],
        'ratio-slider': [
          {
            id: 'level1_rs_1',
            question: 'Adjust the sliders to show the ratio 2:3',
            topic: 'Ratios',
            type: 'ratio-slider',
            config: {
              sliders: [
                { id: 'part1', label: 'Part 1', min: 1, max: 10, step: 1 },
                { id: 'part2', label: 'Part 2', min: 1, max: 10, step: 1 }
              ],
              correctRatio: { part1: 2, part2: 3 },
              tolerance: 0.1
            },
            points: 2
          }
        ],
        'visual-calculator': [
          {
            id: 'level1_vc_1',
            question: 'Calculate 12 × 15 step by step',
            topic: 'Multiplication',
            type: 'visual-calculator',
            config: {
              calculationType: 'standard',
              targetCalculation: '12 × 15',
              expectedResult: 180,
              allowedOperations: ['+', '-', '×', '÷'],
              showSteps: true,
              hint: 'Enter 12, then ×, then 15, then ='
            },
            points: 3
          },
          {
            id: 'level1_vc_2',
            question: 'Calculate 144 ÷ 12 using the calculator',
            topic: 'Division',
            type: 'visual-calculator',
            config: {
              calculationType: 'standard',
              targetCalculation: '144 ÷ 12',
              expectedResult: 12,
              allowedOperations: ['+', '-', '×', '÷'],
              showSteps: true,
              hint: 'Enter 144, then ÷, then 12, then ='
            },
            points: 3
          }
        ],
        'drag-drop': [
          {
            id: 'level1_dd_1',
            question: 'Match each algebraic expression to its simplified form',
            topic: 'Algebra',
            type: 'drag-drop',
            config: {
              items: [
                { id: 'expr1', text: '2x + 3x' },
                { id: 'expr2', text: '4y - 2y' },
                { id: 'expr3', text: '3a × 2b' }
              ],
              zones: [
                { id: 'simp1', label: '5x' },
                { id: 'simp2', label: '2y' },
                { id: 'simp3', label: '6ab' }
              ],
              correctMatches: {
                'simp1': 'expr1',
                'simp2': 'expr2',
                'simp3': 'expr3'
              }
            },
            points: 3
          }
        ],
        'pattern-completion': [
          {
            id: 'level1_pc_1',
            question: 'Complete the pattern: 3, 6, 12, 24, ?',
            topic: 'Geometric Sequences',
            type: 'pattern-completion',
            config: {
              patternType: 'geometric',
              sequence: [3, 6, 12, 24],
              options: [48, 36, 30, 42],
              correctAnswer: 48,
              rule: 'Multiply by 2 each time',
              hint: 'Each number is double the previous one'
            },
            points: 3
          }
        ]
      },
      GCSEPart1: {
        'equation-builder': [
          {
            id: 'gcse1_eb_1',
            question: 'Build the equation to solve: "A number increased by 5 equals 12"',
            topic: 'Algebra',
            type: 'equation-builder',
            config: {
              availableTerms: ['x', '+', '-', '=', '5', '12', '7'],
              correctEquation: ['x', '+', '5', '=', '12'],
              allowedOperations: ['+', '-', '='],
              variables: ['x', 'y']
            },
            points: 3
          },
          {
            id: 'gcse1_eb_2',
            question: 'Build the equation: "Three times a number minus 4 equals 14"',
            topic: 'Algebra',
            type: 'equation-builder',
            config: {
              availableTerms: ['3', 'x', '+', '-', '=', '4', '14', '2'],
              correctEquation: ['3', 'x', '-', '4', '=', '14'],
              allowedOperations: ['+', '-', '×', '='],
              variables: ['x', 'y', 'n']
            },
            points: 3
          }
        ],
        'number-line': [
          {
            id: 'gcse1_nl_1',
            question: 'Mark the solution to x + 3 = 7 on the number line',
            topic: 'Solving Equations',
            type: 'number-line',
            config: {
              min: -2,
              max: 8,
              step: 1,
              snapToGrid: true,
              correctAnswer: 4
            },
            points: 2
          }
        ],
        'step-by-step': [
          {
            id: 'gcse1_sbs_1',
            question: 'Solve 2x + 3 = 11 step by step',
            topic: 'Solving Equations',
            type: 'step-by-step',
            config: {
              steps: [
                {
                  instruction: 'Subtract 3 from both sides: 2x + 3 - 3 = 11 - 3',
                  expectedAnswer: '2x = 8',
                  hint: 'What remains after subtracting 3?'
                },
                {
                  instruction: 'Divide both sides by 2: 2x ÷ 2 = 8 ÷ 2',
                  expectedAnswer: 'x = 4',
                  hint: 'What is 8 divided by 2?'
                }
              ],
              finalAnswer: 'x = 4'
            },
            points: 3
          }
        ],
        'pattern-completion': [
          {
            id: 'gcse1_pc_1',
            question: 'Find the next term in the sequence: 2n + 1, where n = 1, 2, 3, 4, ?',
            topic: 'Algebraic Sequences',
            type: 'pattern-completion',
            config: {
              patternType: 'algebraic',
              sequence: ['3 (n=1)', '5 (n=2)', '7 (n=3)', '9 (n=4)'],
              options: [11, 10, 12, 13],
              correctAnswer: 11,
              rule: '2n + 1, so when n=5: 2(5)+1=11',
              hint: 'Substitute n=5 into the formula 2n+1'
            },
            points: 3
          }
        ]
      },
      GCSEPart2: {
        'pattern-completion': [
          {
            id: 'gcse2_pc_1',
            question: 'Complete the algebraic pattern: 2x, 4x, 6x, 8x, ?',
            topic: 'Algebraic Patterns',
            type: 'pattern-completion',
            config: {
              patternType: 'algebraic',
              sequence: ['2x', '4x', '6x', '8x'],
              options: ['10x', '9x', '12x', '8x²'],
              correctAnswer: '10x',
              rule: 'Add 2x each time',
              hint: 'Look at the coefficient of x in each term'
            },
            points: 4
          },
          {
            id: 'gcse2_pc_2',
            question: 'What comes next in this quadratic sequence: 1, 4, 9, 16, ?',
            topic: 'Quadratic Sequences',
            type: 'pattern-completion',
            config: {
              patternType: 'quadratic',
              sequence: [1, 4, 9, 16],
              options: [20, 25, 24, 32],
              correctAnswer: 25,
              rule: 'Perfect squares: n²',
              hint: 'These are perfect squares: 1², 2², 3², 4², ...'
            },
            points: 4
          }
        ],
        'coordinate-plot': [
          {
            id: 'gcse2_cp_1',
            question: 'Plot the quadratic function y = x² - 2x - 3',
            topic: 'Quadratic Functions',
            type: 'coordinate-plot',
            config: {
              gridSize: { width: 12, height: 12 },
              origin: { x: 6, y: 6 },
              correctPoints: [
                { x: -2, y: 5 }, { x: -1, y: 0 }, { x: 0, y: -3 },
                { x: 1, y: -4 }, { x: 2, y: -3 }, { x: 3, y: 0 }, { x: 4, y: 5 }
              ],
              showGrid: true,
              showAxes: true,
              drawCurve: true
            },
            points: 5
          }
        ],
        'ratio-slider': [
          {
            id: 'gcse2_rs_1',
            question: 'A recipe uses flour and sugar in the ratio 3:2. If you use 150g flour, how much sugar?',
            topic: 'Proportional Reasoning',
            type: 'ratio-slider',
            config: {
              sliders: [
                { id: 'flour', label: 'Flour (g)', min: 100, max: 200, step: 10, fixed: 150 },
                { id: 'sugar', label: 'Sugar (g)', min: 50, max: 150, step: 10 }
              ],
              correctRatio: { flour: 150, sugar: 100 },
              tolerance: 5,
              ratioBase: { flour: 3, sugar: 2 }
            },
            points: 3
          }
        ]
      }
    };
  }

  /**
   * Get interactive questions for a specific level and type
   */
  getQuestions(level, type = null) {
    if (!this.questionData[level]) {
      console.warn(`No questions found for level: ${level}`);
      return [];
    }

    if (type) {
      return this.questionData[level][type] || [];
    }

    // Return all interactive questions for the level
    const allQuestions = [];
    Object.values(this.questionData[level]).forEach(typeQuestions => {
      allQuestions.push(...typeQuestions);
    });
    
    return allQuestions;
  }

  /**
   * Get a random interactive question for a level
   */
  getRandomQuestion(level, type = null) {
    const questions = this.getQuestions(level, type);
    if (questions.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * questions.length);
    return questions[randomIndex];
  }

  /**
   * Get all available question types for a level
   */
  getAvailableTypes(level) {
    if (!this.questionData[level]) return [];
    return Object.keys(this.questionData[level]);
  }

  /**
   * Validate question configuration
   */
  validateQuestion(question) {
    const required = ['id', 'question', 'topic', 'type', 'config', 'points'];
    return required.every(field => question.hasOwnProperty(field));
  }

  /**
   * Get question by ID
   */
  getQuestionById(questionId) {
    for (const level in this.questionData) {
      for (const type in this.questionData[level]) {
        const question = this.questionData[level][type].find(q => q.id === questionId);
        if (question) return question;
      }
    }
    return null;
  }

  /**
   * Get statistics about available questions
   */
  getStatistics() {
    const stats = {
      totalQuestions: 0,
      byLevel: {},
      byType: {}
    };

    for (const level in this.questionData) {
      stats.byLevel[level] = 0;
      
      for (const type in this.questionData[level]) {
        const count = this.questionData[level][type].length;
        stats.byLevel[level] += count;
        stats.byType[type] = (stats.byType[type] || 0) + count;
        stats.totalQuestions += count;
      }
    }

    return stats;
  }
}

// Create global instance
window.InteractiveQuestionData = new InteractiveQuestionData();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = InteractiveQuestionData;
}
