<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Math Questions Test</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="interactive-questions.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-blue-900">Interactive Math Questions Test</h1>
        
        <!-- Equation Builder Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Equation Builder</h2>
            <div class="interactive-question">
                <p class="mb-4">Build the equation: 2x + 5 = 13</p>
                <div id="equation-builder" class="equation-builder-container">
                    <div class="equation-components">
                        <div class="equation-component" draggable="true">2</div>
                        <div class="equation-component" draggable="true">x</div>
                        <div class="equation-component" draggable="true">+</div>
                        <div class="equation-component" draggable="true">5</div>
                        <div class="equation-component" draggable="true">=</div>
                        <div class="equation-component" draggable="true">13</div>
                    </div>
                    <div class="equation-workspace">
                        <div class="equation-drop-zone" data-position="0">?</div>
                        <div class="equation-drop-zone" data-position="1">?</div>
                        <div class="equation-drop-zone" data-position="2">?</div>
                        <div class="equation-drop-zone" data-position="3">?</div>
                        <div class="equation-drop-zone" data-position="4">?</div>
                        <div class="equation-drop-zone" data-position="5">?</div>
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetEquationBuilder()">
                        <span class="btn-icon">🔄</span> Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Graph Plot Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Graph Plotting</h2>
            <div class="interactive-question">
                <p class="mb-4">Click on the graph to plot the point (3, 2)</p>
                <div class="graph-container">
                    <canvas id="graph-canvas" class="graph-canvas" width="400" height="300"></canvas>
                    <div class="graph-coordinates">
                        Click coordinates: <span id="coordinates">(0, 0)</span>
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetGraph()">
                        <span class="btn-icon">🔄</span> Clear Graph
                    </button>
                </div>
            </div>
        </div>

        <!-- Sorting Activity Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Sorting Activity</h2>
            <div class="interactive-question">
                <p class="mb-4">Sort these numbers into Even and Odd categories</p>
                <div class="sorting-container">
                    <div class="sorting-items">
                        <div class="sorting-item" draggable="true" data-value="2">2</div>
                        <div class="sorting-item" draggable="true" data-value="3">3</div>
                        <div class="sorting-item" draggable="true" data-value="4">4</div>
                        <div class="sorting-item" draggable="true" data-value="7">7</div>
                        <div class="sorting-item" draggable="true" data-value="8">8</div>
                        <div class="sorting-item" draggable="true" data-value="9">9</div>
                    </div>
                    <div class="sorting-categories">
                        <div class="sorting-category">
                            <div class="category-label">Even Numbers</div>
                            <div class="category-drop-zone" data-category="even"></div>
                        </div>
                        <div class="sorting-category">
                            <div class="category-label">Odd Numbers</div>
                            <div class="category-drop-zone" data-category="odd"></div>
                        </div>
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetSorting()">
                        <span class="btn-icon">🔄</span> Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Drag and Drop Equation Formation Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Drag and Drop Equation Formation</h2>
            <div class="interactive-question">
                <p class="mb-4">Drag the numbers and symbols to form the equation: 5 + 3 = 8</p>
                <div class="matching-container">
                    <div class="draggable-items">
                        <div class="draggable-item" draggable="true" data-value="5">5</div>
                        <div class="draggable-item" draggable="true" data-value="3">3</div>
                        <div class="draggable-item" draggable="true" data-value="8">8</div>
                        <div class="draggable-item" draggable="true" data-value="+">+</div>
                        <div class="draggable-item" draggable="true" data-value="=">=</div>
                    </div>
                    <div class="drop-zones">
                        <div class="drop-zone" data-label="Position 1" data-target="5"></div>
                        <div class="drop-zone" data-label="Position 2" data-target="+"></div>
                        <div class="drop-zone" data-label="Position 3" data-target="3"></div>
                        <div class="drop-zone" data-label="Position 4" data-target="="></div>
                        <div class="drop-zone" data-label="Position 5" data-target="8"></div>
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetEquationFormation()">
                        <span class="btn-icon">🔄</span> Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Drag and Drop Matching Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Drag and Drop Matching</h2>
            <div class="interactive-question">
                <p class="mb-4">Match fractions with their decimal equivalents</p>
                <div class="matching-container">
                    <div class="draggable-items">
                        <div class="draggable-item" draggable="true" data-value="1/2">1/2</div>
                        <div class="draggable-item" draggable="true" data-value="1/4">1/4</div>
                        <div class="draggable-item" draggable="true" data-value="3/4">3/4</div>
                    </div>
                    <div class="drop-zones">
                        <div class="drop-zone" data-label="0.25" data-target="1/4"></div>
                        <div class="drop-zone" data-label="0.5" data-target="1/2"></div>
                        <div class="drop-zone" data-label="0.75" data-target="3/4"></div>
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetMatching()">
                        <span class="btn-icon">🔄</span> Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Area Model Test -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Area Model (Fraction Bars)</h2>
            <div class="interactive-question">
                <p class="mb-4">Click segments to shade 3/4 of each bar</p>
                <div class="area-model-container">
                    <div class="fraction-bars">
                        <div class="fraction-bar" data-total="4">
                            <div class="fraction-segment" data-index="0"></div>
                            <div class="fraction-segment" data-index="1"></div>
                            <div class="fraction-segment" data-index="2"></div>
                            <div class="fraction-segment" data-index="3"></div>
                        </div>
                        <div class="fraction-bar" data-total="8">
                            <div class="fraction-segment" data-index="0"></div>
                            <div class="fraction-segment" data-index="1"></div>
                            <div class="fraction-segment" data-index="2"></div>
                            <div class="fraction-segment" data-index="3"></div>
                            <div class="fraction-segment" data-index="4"></div>
                            <div class="fraction-segment" data-index="5"></div>
                            <div class="fraction-segment" data-index="6"></div>
                            <div class="fraction-segment" data-index="7"></div>
                        </div>
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetAreaModel()">
                        <span class="btn-icon">🔄</span> Reset
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Equation Builder Functions
        function resetEquationBuilder() {
            const workspace = document.querySelector('.equation-workspace');
            const components = document.querySelector('.equation-components');
            
            // Move all components back to the components area
            workspace.querySelectorAll('.equation-component').forEach(comp => {
                components.appendChild(comp);
            });
            
            // Reset all drop zones
            workspace.querySelectorAll('.equation-drop-zone').forEach(zone => {
                zone.textContent = '?';
                zone.classList.remove('filled');
            });
        }

        // Graph Functions
        function resetGraph() {
            const canvas = document.getElementById('graph-canvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawGrid(ctx, canvas.width, canvas.height);
            document.getElementById('coordinates').textContent = '(0, 0)';
        }

        function drawGrid(ctx, width, height) {
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;
            
            // Draw grid lines
            for (let x = 0; x <= width; x += 40) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }
            
            for (let y = 0; y <= height; y += 30) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
            
            // Draw axes
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, height/2);
            ctx.lineTo(width, height/2);
            ctx.moveTo(width/2, 0);
            ctx.lineTo(width/2, height);
            ctx.stroke();
        }

        // Sorting Functions
        function resetSorting() {
            const itemsContainer = document.querySelector('.sorting-items');
            const categories = document.querySelectorAll('.category-drop-zone');
            
            categories.forEach(category => {
                const items = category.querySelectorAll('.sorting-item');
                items.forEach(item => {
                    itemsContainer.appendChild(item);
                });
            });
        }

        // Equation Formation Functions
        function resetEquationFormation() {
            const itemsContainer = document.querySelector('.draggable-items');
            const dropZones = document.querySelectorAll('.drop-zone');
            
            dropZones.forEach(zone => {
                const items = zone.querySelectorAll('.draggable-item');
                items.forEach(item => {
                    itemsContainer.appendChild(item);
                });
            });
        }

        // Matching Functions
        function resetMatching() {
            const itemsContainer = document.querySelector('.draggable-items');
            const dropZones = document.querySelectorAll('.drop-zone');
            
            dropZones.forEach(zone => {
                const items = zone.querySelectorAll('.draggable-item');
                items.forEach(item => {
                    itemsContainer.appendChild(item);
                });
            });
        }

        // Area Model Functions
        function resetAreaModel() {
            document.querySelectorAll('.fraction-segment').forEach(segment => {
                segment.classList.remove('shaded');
            });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize graph
            const canvas = document.getElementById('graph-canvas');
            const ctx = canvas.getContext('2d');
            drawGrid(ctx, canvas.width, canvas.height);
            
            // Graph click handler
            canvas.addEventListener('click', function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // Convert to graph coordinates
                const graphX = Math.round((x - canvas.width/2) / 40 * 10) / 10;
                const graphY = Math.round((canvas.height/2 - y) / 30 * 10) / 10;
                
                document.getElementById('coordinates').textContent = `(${graphX}, ${graphY})`;
                
                // Draw point
                ctx.fillStyle = '#007bff';
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            // Number line drag handler - removed as no longer needed
            
            // Fraction bar click handlers
            document.querySelectorAll('.fraction-segment').forEach(segment => {
                segment.addEventListener('click', function() {
                    this.classList.toggle('shaded');
                });
            });
            
            // Drag and drop setup for sorting and matching
            setupDragAndDrop();
        });
        
        function setupDragAndDrop() {
            // Add drag event listeners to all draggable items
            document.querySelectorAll('[draggable="true"]').forEach(item => {
                item.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', '');
                    e.dataTransfer.effectAllowed = 'move';
                    this.classList.add('dragging');
                });
                
                item.addEventListener('dragend', function() {
                    this.classList.remove('dragging');
                });
            });
            
            // Add drop event listeners to all drop zones
            document.querySelectorAll('.drop-zone, .category-drop-zone, .equation-drop-zone').forEach(zone => {
                zone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    this.classList.add('drag-over');
                });
                
                zone.addEventListener('dragleave', function() {
                    this.classList.remove('drag-over');
                });
                
                zone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('drag-over');
                    
                    const dragging = document.querySelector('.dragging');
                    if (dragging) {
                        this.appendChild(dragging);
                        
                        // Special handling for equation builder
                        if (this.classList.contains('equation-drop-zone')) {
                            this.textContent = '';
                            this.classList.add('filled');
                        }
                    }
                });
            });
        }
    </script>
</body>
</html>
