/**
 * Test script for Enhanced Mathematics Assessment Question Generation
 * This script tests the improved OpenAI prompts for generating high-quality mathematics questions
 */

const { OpenAI } = require('openai');
require('dotenv').config();

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Import the enhanced prompt function (simplified version for testing)
function createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel) {
  const levelConfig = {
    'Entry': {
      description: 'Entry Level Mathematics - Foundation skills for adult learners',
      topics: {
        arithmetic: { weight: 30, description: 'Basic operations (+, -, ×, ÷) with whole numbers and simple decimals' },
        fractions: { weight: 20, description: 'Simple fractions (halves, quarters, thirds), basic operations' },
        percentages: { weight: 15, description: 'Common percentages (10%, 25%, 50%), percentage of amounts' },
        measurement: { weight: 15, description: 'Length, weight, capacity, time - basic conversions' },
        algebra: { weight: 10, description: 'Simple equations (x + a = b), basic substitution' },
        dataHandling: { weight: 10, description: 'Reading charts, simple averages, basic statistics' }
      },
      difficulty: 'Foundation level - clear, practical contexts',
      pointRange: [1, 2],
      examples: [
        '{"id":1,"type":"multiple-choice","topic":"arithmetic","question":"A shop sells apples for £1.20 per kilogram. How much would 3 kilograms cost?","options":["£2.60","£3.60","£4.20","£3.20"],"correctAnswer":"£3.60","points":2,"explanation":"3 × £1.20 = £3.60"}',
        '{"id":2,"type":"numeric","topic":"measurement","question":"Convert 2.5 metres to centimetres.","correctAnswer":"250","points":1,"explanation":"1 metre = 100 centimetres, so 2.5 × 100 = 250 cm"}'
      ]
    },
    'Level1': {
      description: 'Level 1 Mathematics - Intermediate skills building on foundation',
      topics: {
        arithmetic: { weight: 25, description: 'Multi-step calculations, order of operations, negative numbers' },
        fractions: { weight: 20, description: 'Operations with fractions, mixed numbers, fraction-decimal conversion' },
        percentages: { weight: 15, description: 'Percentage increase/decrease, finding percentages of amounts' },
        algebra: { weight: 15, description: 'Linear equations, substitution, simple expressions' },
        geometry: { weight: 15, description: 'Area, perimeter, angles, basic shapes' },
        statistics: { weight: 10, description: 'Mean, median, mode, range, interpreting data' }
      },
      difficulty: 'Intermediate level - practical applications',
      pointRange: [2, 3],
      examples: [
        '{"id":1,"type":"multiple-choice","topic":"percentages","question":"A jacket originally costs £80. In a sale, it is reduced by 25%. What is the sale price?","options":["£55","£60","£65","£70"],"correctAnswer":"£60","points":2,"explanation":"25% of £80 = £20, so sale price = £80 - £20 = £60"}'
      ]
    }
  };

  const config = levelConfig[level];
  const totalQuestions = questionSpecs.count;
  
  // Calculate topic distribution
  const topicDistribution = Object.entries(config.topics).map(([topic, info]) => {
    const count = Math.round((info.weight / 100) * totalQuestions);
    return `${topic}: ${count} questions (${info.description})`;
  }).join('\n');

  return `Generate exactly ${totalQuestions} high-quality mathematics questions for ${config.description}.

EDUCATIONAL OBJECTIVES:
- Assess understanding, not just memorization
- Use clear, unambiguous language appropriate for adult learners
- Include practical, real-world contexts where relevant
- Follow UK mathematical terminology and educational standards
- Ensure progressive difficulty within the level

TOPIC DISTRIBUTION (${totalQuestions} questions total):
${topicDistribution}

QUESTION QUALITY STANDARDS:
1. MATHEMATICAL ACCURACY: All calculations must be correct
2. CLEAR LANGUAGE: Use simple, direct language avoiding jargon
3. REALISTIC CONTEXTS: Use practical scenarios relevant to adult learners
4. APPROPRIATE DIFFICULTY: Match the ${level} level expectations
5. EDUCATIONAL VALUE: Each question should teach or assess specific skills

QUESTION TYPES & REQUIREMENTS:
1. "multiple-choice" (60% of questions):
   - Exactly 4 options with one clearly correct answer
   - Distractors should represent common mathematical errors
   - Options should be plausible and well-distributed
   
2. "numeric" (30% of questions):
   - Require specific numerical calculations
   - Accept reasonable precision (2-3 decimal places)
   - Include appropriate units in the answer
   
3. "short-answer" (10% of questions):
   - For algebraic expressions, simplified fractions, equations
   - Answers should be unambiguous and in standard form

REQUIRED JSON STRUCTURE:
{
  "id": [sequential number],
  "type": "[multiple-choice|numeric|short-answer]",
  "topic": "[arithmetic|fractions|percentages|algebra|geometry|statistics|measurement|trigonometry|probability|dataHandling]",
  "question": "[clear, well-worded question with context]",
  "options": [array of 4 strings - only for multiple-choice],
  "correctAnswer": "[exact answer as string]",
  "points": [integer from ${config.pointRange[0]} to ${config.pointRange[1]}],
  "explanation": "[clear step-by-step explanation for learning]"
}

VALIDATION CHECKLIST (self-check before responding):
✓ All questions mathematically accurate
✓ Language appropriate for adult learners
✓ Realistic, practical contexts used
✓ Topic distribution follows specified weights
✓ Question types distributed as specified (60% MC, 30% numeric, 10% short)
✓ All required JSON fields present
✓ Explanations are clear and educational

EXAMPLES:
${config.examples.join('\n')}

Generate exactly ${totalQuestions} questions following all guidelines above:`;
}

// Test function to generate and analyze questions
async function testEnhancedPrompts() {
  console.log('🧪 Testing Enhanced Mathematics Question Generation Prompts\n');
  
  const testCases = [
    { level: 'Entry', count: 10 },
    { level: 'Level1', count: 12 }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📝 Testing ${testCase.level} level with ${testCase.count} questions`);
    console.log('='.repeat(60));
    
    try {
      const startTime = Date.now();
      
      // Generate the prompt
      const prompt = createOptimizedMathQuestionPrompt(testCase.level, { count: testCase.count }, 'adult-learner');
      
      console.log('Generated prompt length:', prompt.length, 'characters');
      
      // Call OpenAI API
      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are an expert mathematics assessment designer specializing in UK educational standards. Your role is to create high-quality, educationally valuable mathematics questions for adult learners.

CORE PRINCIPLES:
- Mathematical accuracy is paramount - double-check all calculations
- Use clear, accessible language appropriate for adult learners
- Create realistic, practical contexts that relate to everyday life
- Follow UK mathematical terminology and conventions
- Ensure questions test understanding, not just memorization

QUALITY STANDARDS:
- Questions must be unambiguous with exactly one correct answer
- Multiple choice distractors should represent common mathematical errors
- Explanations should be educational and help learners understand concepts
- Difficulty should be appropriate for the specified level
- Use proper mathematical notation and terminology

RESPONSE FORMAT:
- Return ONLY a valid JSON array
- Include all required fields for each question
- Ensure consistent formatting and structure
- Validate mathematical accuracy before responding`
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1200,
        temperature: 0.1,
        top_p: 0.95,
        frequency_penalty: 0.2
      });
      
      const generationTime = Date.now() - startTime;
      console.log(`⏱️  Generation time: ${generationTime}ms`);
      
      // Parse and analyze the response
      const responseText = completion.choices[0].message.content;
      console.log('Response length:', responseText.length, 'characters');
      
      let questions;
      try {
        questions = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ JSON parsing failed:', parseError.message);
        console.log('Raw response:', responseText.substring(0, 500) + '...');
        continue;
      }
      
      // Validate and analyze questions
      console.log(`✅ Successfully generated ${questions.length} questions`);
      
      // Analyze topic distribution
      const topicCounts = {};
      const typeCounts = {};
      let totalPoints = 0;
      let validQuestions = 0;
      
      questions.forEach((q, index) => {
        // Basic validation
        const hasRequiredFields = q.id && q.type && q.topic && q.question && q.correctAnswer && q.points && q.explanation;
        
        if (hasRequiredFields) {
          validQuestions++;
          topicCounts[q.topic] = (topicCounts[q.topic] || 0) + 1;
          typeCounts[q.type] = (typeCounts[q.type] || 0) + 1;
          totalPoints += q.points;
          
          // Validate multiple choice options
          if (q.type === 'multiple-choice') {
            if (!q.options || q.options.length !== 4) {
              console.warn(`⚠️  Question ${index + 1}: Multiple choice should have 4 options`);
            } else if (!q.options.includes(q.correctAnswer)) {
              console.warn(`⚠️  Question ${index + 1}: Correct answer not in options`);
            }
          }
        } else {
          console.warn(`⚠️  Question ${index + 1}: Missing required fields`);
        }
      });
      
      console.log(`\n📊 Analysis Results:`);
      console.log(`Valid questions: ${validQuestions}/${questions.length}`);
      console.log(`Total points: ${totalPoints}`);
      console.log(`Average points per question: ${(totalPoints / questions.length).toFixed(1)}`);
      
      console.log('\nTopic Distribution:');
      Object.entries(topicCounts).forEach(([topic, count]) => {
        const percentage = Math.round((count / questions.length) * 100);
        console.log(`  ${topic}: ${count} questions (${percentage}%)`);
      });
      
      console.log('\nQuestion Type Distribution:');
      Object.entries(typeCounts).forEach(([type, count]) => {
        const percentage = Math.round((count / questions.length) * 100);
        console.log(`  ${type}: ${count} questions (${percentage}%)`);
      });
      
      // Show sample questions
      console.log('\n📝 Sample Questions:');
      questions.slice(0, 2).forEach((q, index) => {
        console.log(`\nQuestion ${index + 1} (${q.type}, ${q.topic}, ${q.points} points):`);
        console.log(`Q: ${q.question}`);
        if (q.options) {
          q.options.forEach((option, i) => console.log(`  ${String.fromCharCode(65 + i)}) ${option}`));
        }
        console.log(`A: ${q.correctAnswer}`);
        console.log(`Explanation: ${q.explanation}`);
      });
      
    } catch (error) {
      console.error(`❌ Error testing ${testCase.level}:`, error.message);
    }
  }
  
  console.log('\n🎉 Enhanced prompt testing completed!');
}

// Run the test
if (require.main === module) {
  testEnhancedPrompts().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testEnhancedPrompts, createOptimizedMathQuestionPrompt };
