/**
 * Test script for Mathematics Assessment Loading Performance Optimizations
 * This script tests the various performance improvements implemented for the mathematics assessment
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_LEVELS = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];

// Performance metrics
const performanceMetrics = {
  cacheWarmingTime: 0,
  assessmentStartTimes: {},
  cacheHitRates: {},
  totalTests: 0,
  successfulTests: 0,
  errors: []
};

/**
 * Test cache warming functionality
 */
async function testCacheWarming() {
  console.log('\n🔥 Testing Cache Warming Performance...');
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${BASE_URL}/api/math-assessments/cache/warm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        levels: TEST_LEVELS,
        background: false
      }),
    });

    if (!response.ok) {
      throw new Error(`Cache warming failed: ${response.status}`);
    }

    const data = await response.json();
    const warmingTime = Date.now() - startTime;
    
    performanceMetrics.cacheWarmingTime = warmingTime;
    
    console.log(`✅ Cache warming completed in ${warmingTime}ms`);
    console.log(`📊 Results:`, data.results);
    console.log(`💾 Cache size: ${data.cacheSize}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Cache warming test failed:`, error.message);
    performanceMetrics.errors.push(`Cache warming: ${error.message}`);
    return false;
  }
}

/**
 * Test assessment start performance for each level
 */
async function testAssessmentStartPerformance() {
  console.log('\n⚡ Testing Assessment Start Performance...');
  
  for (const level of TEST_LEVELS) {
    console.log(`\n📝 Testing ${level} level...`);
    const times = [];
    
    // Test each level 3 times to get average
    for (let i = 0; i < 3; i++) {
      const startTime = Date.now();
      
      try {
        const response = await fetch(`${BASE_URL}/api/math-assessments/start`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            level: level,
            email: `${TEST_EMAIL}_${level}_${i}`,
            studentLevel: 'adult-learner'
          }),
        });

        if (!response.ok) {
          throw new Error(`Assessment start failed: ${response.status}`);
        }

        const data = await response.json();
        const responseTime = Date.now() - startTime;
        times.push(responseTime);
        
        console.log(`  Attempt ${i + 1}: ${responseTime}ms (${data.questions.length} questions)`);
        
        performanceMetrics.totalTests++;
        performanceMetrics.successfulTests++;
        
      } catch (error) {
        console.error(`  ❌ Attempt ${i + 1} failed:`, error.message);
        performanceMetrics.errors.push(`${level} assessment start: ${error.message}`);
        performanceMetrics.totalTests++;
      }
    }
    
    if (times.length > 0) {
      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      
      performanceMetrics.assessmentStartTimes[level] = {
        average: averageTime,
        min: minTime,
        max: maxTime,
        attempts: times.length
      };
      
      console.log(`  📊 ${level} Average: ${averageTime.toFixed(0)}ms (min: ${minTime}ms, max: ${maxTime}ms)`);
    }
  }
}

/**
 * Test cache performance metrics
 */
async function testCachePerformance() {
  console.log('\n📈 Testing Cache Performance Metrics...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/math-assessments/performance`);
    
    if (!response.ok) {
      throw new Error(`Performance metrics failed: ${response.status}`);
    }

    const data = await response.json();
    const metrics = data.metrics;
    
    console.log('📊 Performance Metrics:');
    console.log(`  Total Requests: ${metrics.totalRequests}`);
    console.log(`  Cache Hits: ${metrics.cacheHits}`);
    console.log(`  Cache Misses: ${metrics.cacheMisses}`);
    console.log(`  Cache Hit Rate: ${((metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100).toFixed(1)}%`);
    console.log(`  Fallback Usage: ${metrics.fallbackUsage}`);
    console.log(`  Average Generation Time: ${metrics.averageGenerationTime.toFixed(0)}ms`);
    console.log(`  API Timeouts: ${metrics.apiTimeouts}`);
    console.log(`  Successful Retries: ${metrics.successfulRetries}`);
    
    performanceMetrics.cacheHitRates = {
      hitRate: (metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100,
      totalRequests: metrics.totalRequests,
      cacheHits: metrics.cacheHits,
      cacheMisses: metrics.cacheMisses,
      averageTime: metrics.averageGenerationTime
    };
    
    return true;
  } catch (error) {
    console.error(`❌ Cache performance test failed:`, error.message);
    performanceMetrics.errors.push(`Cache performance: ${error.message}`);
    return false;
  }
}

/**
 * Test background cache warming
 */
async function testBackgroundCacheWarming() {
  console.log('\n🚀 Testing Background Cache Warming...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/math-assessments/cache/warm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        levels: ['Entry'],
        background: true
      }),
    });

    if (!response.ok) {
      throw new Error(`Background cache warming failed: ${response.status}`);
    }

    const data = await response.json();
    console.log(`✅ Background cache warming initiated: ${data.message}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Background cache warming test failed:`, error.message);
    performanceMetrics.errors.push(`Background cache warming: ${error.message}`);
    return false;
  }
}

/**
 * Generate performance report
 */
function generatePerformanceReport() {
  console.log('\n📋 PERFORMANCE OPTIMIZATION REPORT');
  console.log('=====================================');
  
  console.log(`\n🎯 Test Summary:`);
  console.log(`  Total Tests: ${performanceMetrics.totalTests}`);
  console.log(`  Successful Tests: ${performanceMetrics.successfulTests}`);
  console.log(`  Success Rate: ${((performanceMetrics.successfulTests / performanceMetrics.totalTests) * 100).toFixed(1)}%`);
  
  if (performanceMetrics.cacheWarmingTime > 0) {
    console.log(`\n🔥 Cache Warming:`);
    console.log(`  Time: ${performanceMetrics.cacheWarmingTime}ms`);
    console.log(`  Status: ${performanceMetrics.cacheWarmingTime < 10000 ? '✅ Good' : '⚠️ Slow'}`);
  }
  
  console.log(`\n⚡ Assessment Start Times:`);
  Object.entries(performanceMetrics.assessmentStartTimes).forEach(([level, metrics]) => {
    const status = metrics.average < 3000 ? '✅ Excellent' : 
                   metrics.average < 5000 ? '✅ Good' : 
                   metrics.average < 10000 ? '⚠️ Acceptable' : '❌ Slow';
    console.log(`  ${level}: ${metrics.average.toFixed(0)}ms avg (${metrics.min}-${metrics.max}ms) ${status}`);
  });
  
  if (performanceMetrics.cacheHitRates.hitRate !== undefined) {
    console.log(`\n📈 Cache Performance:`);
    console.log(`  Hit Rate: ${performanceMetrics.cacheHitRates.hitRate.toFixed(1)}%`);
    console.log(`  Average Time: ${performanceMetrics.cacheHitRates.averageTime.toFixed(0)}ms`);
    const cacheStatus = performanceMetrics.cacheHitRates.hitRate > 70 ? '✅ Excellent' :
                       performanceMetrics.cacheHitRates.hitRate > 50 ? '✅ Good' : '⚠️ Needs Improvement';
    console.log(`  Status: ${cacheStatus}`);
  }
  
  if (performanceMetrics.errors.length > 0) {
    console.log(`\n❌ Errors (${performanceMetrics.errors.length}):`);
    performanceMetrics.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }
  
  console.log('\n🎉 Performance optimization testing completed!');
}

/**
 * Main test runner
 */
async function runPerformanceTests() {
  console.log('🧪 Mathematics Assessment Loading Performance Tests');
  console.log('==================================================');
  
  // Test cache warming
  await testCacheWarming();
  
  // Wait a moment for cache to be ready
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test assessment start performance
  await testAssessmentStartPerformance();
  
  // Test cache performance metrics
  await testCachePerformance();
  
  // Test background cache warming
  await testBackgroundCacheWarming();
  
  // Generate report
  generatePerformanceReport();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runPerformanceTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runPerformanceTests,
  performanceMetrics
};
