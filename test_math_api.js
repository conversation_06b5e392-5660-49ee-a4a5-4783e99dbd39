require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');

// Test the actual mathematics assessment API endpoint
async function testMathematicsAssessmentAPI() {
  console.log('\n=== Testing Mathematics Assessment API Endpoint ===');

  try {
    // Start mathematics assessment
    const startResponse = await axios.post('http://localhost:3000/api/math-assessments/start', {
      email: '<EMAIL>',
      level: 'Level1',
      studentLevel: 'adult-learner'
    });

    console.log('✅ Assessment started successfully');
    console.log('Assessment ID:', startResponse.data.assessmentId);
    console.log('Question count:', startResponse.data.questions.length);
    console.log('Time limit:', startResponse.data.timeLimit);

    // Check if questions are AI-generated (not fallback)
    const questions = startResponse.data.questions;
    const hasVariedQuestions = questions.some(q => q.topic && q.explanation);

    if (hasVariedQuestions) {
      console.log('✅ Questions appear to be AI-generated (have topics and explanations)');
      console.log('Sample question:', {
        question: questions[0].question,
        topic: questions[0].topic,
        hasExplanation: !!questions[0].explanation
      });
    } else {
      console.log('⚠️  Questions appear to be fallback questions');
    }

    return startResponse.data;

  } catch (error) {
    console.error('❌ API test failed:', error.response?.data || error.message);
    return null;
  }
}

// Test performance metrics endpoint
async function testPerformanceMetrics() {
  console.log('\n=== Testing Performance Metrics ===');

  try {
    const response = await axios.get('http://localhost:3000/api/math-assessments/performance');
    console.log('✅ Performance metrics retrieved');
    console.log('Metrics:', JSON.stringify(response.data.metrics, null, 2));

    return response.data.metrics;
  } catch (error) {
    console.error('❌ Performance metrics test failed:', error.response?.data || error.message);
    return null;
  }
}

// Test the mathematics question generation API call
async function testMathematicsAPI() {
  const API_TIMEOUT_THRESHOLD = 5000; // 5 seconds
  
  const prompt = `Generate 13 mathematics questions for Level1 assessment.

Each question should:
- Be appropriate for adult learners at Level1 mathematics level
- Have 4 multiple choice options (A, B, C, D)
- Have exactly one correct answer
- Include a brief explanation
- Cover topics like: basic arithmetic, fractions, decimals, percentages, basic geometry, data handling, problem solving

Return as valid JSON array with this structure:
[
  {
    "id": 1,
    "type": "multiple_choice",
    "topic": "arithmetic",
    "question": "What is 15 + 27?",
    "options": ["40", "41", "42", "43"],
    "correctAnswer": "42",
    "points": 1,
    "explanation": "15 + 27 = 42"
  }
]

Generate exactly 13 questions with varied types and appropriate difficulty for Level1 level.`;

  console.log('Testing mathematics API call...');
  console.log('Timeout threshold:', API_TIMEOUT_THRESHOLD, 'ms');
  
  const startTime = Date.now();
  
  try {
    // Create a timeout promise for API call
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('API timeout')), API_TIMEOUT_THRESHOLD);
    });

    // Race between API call and timeout
    const apiPromise = openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are a mathematics assessment expert. Generate exactly the requested number of questions in valid JSON format. Be concise and accurate."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.2
    });

    console.log('Making API call...');
    const completion = await Promise.race([apiPromise, timeoutPromise]);
    
    const generationTime = Date.now() - startTime;
    console.log(`API call completed in ${generationTime}ms`);
    
    const response = completion.choices[0].message.content;
    console.log('Response length:', response.length);
    console.log('First 200 characters:', response.substring(0, 200));
    
    // Try to parse JSON
    try {
      const questions = JSON.parse(response);
      console.log('Successfully parsed JSON. Question count:', questions.length);
      console.log('First question:', questions[0]);
      return questions;
    } catch (parseError) {
      console.error('JSON parsing failed:', parseError.message);
      console.log('Raw response:', response);
      return null;
    }
    
  } catch (error) {
    const generationTime = Date.now() - startTime;
    console.error(`API call failed after ${generationTime}ms:`, error.message);
    
    if (error.message === 'API timeout') {
      console.log('This is a timeout error - API call took longer than', API_TIMEOUT_THRESHOLD, 'ms');
    } else {
      console.log('This is a different error:', error);
    }
    
    return null;
  }
}

// Test different timeout values
async function testWithDifferentTimeouts() {
  const timeouts = [5000, 10000, 15000, 30000]; // 5s, 10s, 15s, 30s
  
  for (const timeout of timeouts) {
    console.log(`\n=== Testing with ${timeout}ms timeout ===`);
    
    const startTime = Date.now();
    
    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API timeout')), timeout);
      });

      const apiPromise = openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: "You are a mathematics assessment expert. Generate exactly the requested number of questions in valid JSON format. Be concise and accurate."
          },
          {
            role: "user",
            content: "Generate 5 simple mathematics questions for Level1 assessment in JSON format."
          }
        ],
        max_tokens: 800,
        temperature: 0.2
      });

      const completion = await Promise.race([apiPromise, timeoutPromise]);
      const generationTime = Date.now() - startTime;
      
      console.log(`✅ SUCCESS: API call completed in ${generationTime}ms`);
      console.log('Response length:', completion.choices[0].message.content.length);
      
    } catch (error) {
      const generationTime = Date.now() - startTime;
      console.log(`❌ FAILED: ${error.message} after ${generationTime}ms`);
    }
  }
}

// Run comprehensive tests
async function runTests() {
  console.log('🧮 Starting comprehensive mathematics assessment tests...\n');

  // Test 1: Direct OpenAI API call
  console.log('=== Test 1: Direct OpenAI API Call ===');
  await testMathematicsAPI();

  // Test 2: Different timeout values
  console.log('\n=== Test 2: Different Timeout Values ===');
  await testWithDifferentTimeouts();

  // Test 3: Mathematics Assessment API Endpoint
  console.log('\n=== Test 3: Full Assessment API Integration ===');
  const assessmentData = await testMathematicsAssessmentAPI();

  // Test 4: Performance Metrics
  await testPerformanceMetrics();

  // Test 5: Multiple assessments to test caching
  if (assessmentData) {
    console.log('\n=== Test 5: Testing Caching with Multiple Requests ===');

    for (let i = 1; i <= 3; i++) {
      console.log(`\n--- Request ${i} ---`);
      const startTime = Date.now();

      try {
        const response = await axios.post('http://localhost:3000/api/math-assessments/start', {
          email: `test${i}@example.com`,
          level: 'Level1',
          studentLevel: 'adult-learner'
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ Request ${i} completed in ${requestTime}ms`);
        console.log(`Questions: ${response.data.questions.length}`);

      } catch (error) {
        console.error(`❌ Request ${i} failed:`, error.response?.data || error.message);
      }
    }

    // Check final performance metrics
    console.log('\n--- Final Performance Metrics ---');
    await testPerformanceMetrics();
  }

  console.log('\n🎉 All tests completed!');
}

// Start server check
async function checkServerStatus() {
  try {
    const response = await axios.get('http://localhost:3000/api/math-assessments/performance');
    console.log('✅ Server is running and accessible');
    return true;
  } catch (error) {
    console.log('❌ Server is not accessible. Please start the server first with: node server.js');
    console.log('Error:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🔧 Mathematics Assessment System Debug Tool\n');

  const serverRunning = await checkServerStatus();
  if (serverRunning) {
    await runTests();
  } else {
    console.log('\n📝 To start the server, run: node server.js');
    console.log('Then run this test again: node test_math_api.js');
  }
}

main().catch(console.error);
