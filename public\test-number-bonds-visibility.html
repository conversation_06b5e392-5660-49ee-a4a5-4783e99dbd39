<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Number Bonds Visibility Test</title>
    <link rel="stylesheet" href="mathAssessment.css">
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            background: #f8f9fa;
            border: 3px solid #000000;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .visibility-issue {
            background: #f8d7da;
            border: 2px solid #721c24;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #721c24;
            font-weight: bold;
        }
        
        .visibility-fixed {
            background: #d4edda;
            border: 2px solid #155724;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
            font-weight: bold;
        }
        
        .contrast-test {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-item {
            border: 2px solid #000000;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .debug-info {
            font-size: 12px;
            background: #ffffff;
            border: 1px solid #000000;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Number Bonds Visibility Test & Fix</h1>
        <p>Testing and fixing visibility issues in number bonds questions.</p>
        
        <div class="test-section">
            <h2>Current Number Bonds Implementation</h2>
            <div class="number-bonds-container">
                <div class="bonds-visual-container">
                    <div class="bond-equation">
                        <div class="bond-visual">8</div>
                        <div class="bond-operator">+</div>
                        <div class="bond-visual missing">
                            <div class="missing-placeholder">?</div>
                        </div>
                        <div class="bond-equals">=</div>
                        <div class="bond-result">15</div>
                    </div>
                </div>
                <div class="bonds-input-container">
                    <div class="bond-input-area">
                        <span class="bond-prompt">Enter the missing number:</span>
                        <input type="number" class="bond-input" placeholder="?" value="7">
                        <button class="check-btn">Check</button>
                    </div>
                </div>
            </div>
            
            <button onclick="analyzeVisibility()" style="background: #000000; color: #ffffff; border: 3px solid #ffffff; padding: 12px 24px; border-radius: 8px; font-size: 16px; font-weight: 700; cursor: pointer; margin: 15px 0;">
                Analyze Current Visibility
            </button>
            
            <div id="visibility-analysis"></div>
        </div>
        
        <div class="test-section">
            <h2>Visual Elements Analysis</h2>
            <div class="contrast-test">
                <div class="test-item">
                    <h3>Bond Visual (Numbers)</h3>
                    <div class="bond-visual">8</div>
                    <div class="debug-info" id="bond-visual-info">Click analyze to see details</div>
                </div>
                
                <div class="test-item">
                    <h3>Missing Placeholder</h3>
                    <div class="bond-visual missing">
                        <div class="missing-placeholder">?</div>
                    </div>
                    <div class="debug-info" id="missing-placeholder-info">Click analyze to see details</div>
                </div>
            </div>
            
            <div class="contrast-test">
                <div class="test-item">
                    <h3>Bond Result</h3>
                    <div class="bond-result">15</div>
                    <div class="debug-info" id="bond-result-info">Click analyze to see details</div>
                </div>
                
                <div class="test-item">
                    <h3>Bond Input</h3>
                    <input type="number" class="bond-input" value="7">
                    <div class="debug-info" id="bond-input-info">Click analyze to see details</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Potential Issues & Fixes</h2>
            <div id="issues-and-fixes">
                <p>Click "Analyze Current Visibility" to identify potential issues...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Enhanced Version (If Issues Found)</h2>
            <div id="enhanced-version" style="display: none;">
                <!-- Enhanced version will be shown here if issues are found -->
            </div>
        </div>
    </div>

    <script>
        function analyzeVisibility() {
            const elements = [
                { selector: '.bond-visual', name: 'Bond Visual (Numbers)', infoId: 'bond-visual-info' },
                { selector: '.missing-placeholder', name: 'Missing Placeholder', infoId: 'missing-placeholder-info' },
                { selector: '.bond-result', name: 'Bond Result', infoId: 'bond-result-info' },
                { selector: '.bond-input', name: 'Bond Input', infoId: 'bond-input-info' }
            ];
            
            let analysisHTML = '<h3>Visibility Analysis Results</h3>';
            let issuesFound = [];
            
            elements.forEach(element => {
                const el = document.querySelector(element.selector);
                if (el) {
                    const styles = window.getComputedStyle(el);
                    const color = styles.color;
                    const backgroundColor = styles.backgroundColor;
                    const fontSize = styles.fontSize;
                    const fontWeight = styles.fontWeight;
                    const border = styles.border;
                    
                    // Update debug info
                    const infoEl = document.getElementById(element.infoId);
                    if (infoEl) {
                        infoEl.innerHTML = `
                            Color: ${color}<br>
                            Background: ${backgroundColor}<br>
                            Font Size: ${fontSize}<br>
                            Font Weight: ${fontWeight}<br>
                            Border: ${border}
                        `;
                    }
                    
                    // Check for potential issues
                    let hasIssue = false;
                    let issueDescription = '';
                    
                    // Check if colors are too similar (basic check)
                    if (color === backgroundColor) {
                        hasIssue = true;
                        issueDescription = 'Text color same as background color - invisible!';
                    } else if (color === 'rgba(0, 0, 0, 0)' || backgroundColor === 'rgba(0, 0, 0, 0)') {
                        hasIssue = true;
                        issueDescription = 'Transparent color detected - may cause visibility issues';
                    } else if (color.includes('rgb(255, 255, 255)') && backgroundColor.includes('rgb(255, 255, 255)')) {
                        hasIssue = true;
                        issueDescription = 'White text on white background - invisible!';
                    } else if (color.includes('rgb(0, 0, 0)') && backgroundColor.includes('rgb(0, 0, 0)')) {
                        hasIssue = true;
                        issueDescription = 'Black text on black background - invisible!';
                    }
                    
                    if (hasIssue) {
                        issuesFound.push({
                            name: element.name,
                            issue: issueDescription,
                            selector: element.selector,
                            currentColor: color,
                            currentBackground: backgroundColor
                        });
                    }
                    
                    analysisHTML += `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #000; border-radius: 4px; ${hasIssue ? 'background: #f8d7da; border-color: #721c24;' : 'background: #d4edda; border-color: #155724;'}">
                            <strong>${element.name}:</strong> ${hasIssue ? '❌ ISSUE FOUND' : '✅ OK'}<br>
                            ${hasIssue ? `<span style="color: #721c24;">${issueDescription}</span>` : '<span style="color: #155724;">No visibility issues detected</span>'}
                        </div>
                    `;
                }
            });
            
            document.getElementById('visibility-analysis').innerHTML = analysisHTML;
            
            // Show issues and fixes
            let issuesHTML = '';
            if (issuesFound.length > 0) {
                issuesHTML += '<div class="visibility-issue">🚨 VISIBILITY ISSUES FOUND:</div>';
                
                issuesFound.forEach(issue => {
                    issuesHTML += `
                        <div class="visibility-issue">
                            <strong>${issue.name}</strong><br>
                            Issue: ${issue.issue}<br>
                            Current: ${issue.currentColor} on ${issue.currentBackground}
                        </div>
                    `;
                });
                
                // Show enhanced version
                document.getElementById('enhanced-version').style.display = 'block';
                document.getElementById('enhanced-version').innerHTML = createEnhancedVersion(issuesFound);
                
            } else {
                issuesHTML = '<div class="visibility-fixed">✅ No visibility issues found! All elements have proper contrast.</div>';
            }
            
            document.getElementById('issues-and-fixes').innerHTML = issuesHTML;
        }
        
        function createEnhancedVersion(issues) {
            let enhancedHTML = '<h3>Enhanced Version with Fixes</h3>';
            enhancedHTML += '<div class="visibility-fixed">The following fixes will be applied:</div>';
            
            issues.forEach(issue => {
                let fix = '';
                switch (issue.selector) {
                    case '.bond-visual':
                        fix = 'Black text (#000000) on white background (#ffffff) with strong border';
                        break;
                    case '.missing-placeholder':
                        fix = 'Red text (#dc3545) on light gray background (#f8f9fa) with high contrast';
                        break;
                    case '.bond-result':
                        fix = 'Black text (#000000) on light green background (#d4edda) with dark border';
                        break;
                    case '.bond-input':
                        fix = 'Black text (#000000) on white background (#ffffff) with black border';
                        break;
                }
                
                enhancedHTML += `
                    <div style="margin: 10px 0; padding: 10px; background: #d4edda; border: 1px solid #155724; border-radius: 4px;">
                        <strong>${issue.name}:</strong> ${fix}
                    </div>
                `;
            });
            
            // Create enhanced number bonds example
            enhancedHTML += `
                <div style="margin: 20px 0;">
                    <h4>Enhanced Number Bonds Example:</h4>
                    <div class="number-bonds-container" style="background: #ffffff; border: 3px solid #000000;">
                        <div class="bonds-visual-container" style="background: #ffffff; border: 3px solid #000000;">
                            <div class="bond-equation">
                                <div style="background: #ffffff; border: 3px solid #000000; color: #000000; padding: 15px; border-radius: 8px; font-weight: 900; font-size: 20px;">8</div>
                                <div style="color: #000000; font-weight: 900; font-size: 28px;">+</div>
                                <div style="background: #f8f9fa; border: 3px dashed #dc3545; color: #dc3545; padding: 15px; border-radius: 8px; font-weight: 900; font-size: 28px;">?</div>
                                <div style="color: #000000; font-weight: 900; font-size: 28px;">=</div>
                                <div style="background: #d4edda; border: 3px solid #155724; color: #000000; padding: 15px; border-radius: 8px; font-weight: 900; font-size: 20px;">15</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            return enhancedHTML;
        }
        
        // Auto-run analysis on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(analyzeVisibility, 1000);
        });
    </script>
</body>
</html>
