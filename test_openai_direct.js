require('dotenv').config();
const OpenAI = require('openai');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');

// Test different timeout values and request sizes
async function testOpenAIWithDifferentParams() {
  const tests = [
    {
      name: "Simple 3 questions - 30s timeout",
      timeout: 30000,
      questionCount: 3,
      maxTokens: 800
    },
    {
      name: "Medium 5 questions - 45s timeout", 
      timeout: 45000,
      questionCount: 5,
      maxTokens: 1000
    },
    {
      name: "Full 13 questions - 60s timeout",
      timeout: 60000,
      questionCount: 13,
      maxTokens: 1500
    }
  ];

  for (const test of tests) {
    console.log(`\n=== ${test.name} ===`);
    
    const prompt = `Generate ${test.questionCount} simple mathematics questions for Level1 assessment.

Each question should:
- Be appropriate for adult learners
- Have 4 multiple choice options
- Have exactly one correct answer
- Include a brief explanation

Return as valid JSON array:
[
  {
    "id": 1,
    "question": "What is 15 + 27?",
    "options": ["40", "41", "42", "43"],
    "correctAnswer": "42",
    "explanation": "15 + 27 = 42"
  }
]

Generate exactly ${test.questionCount} questions.`;

    const startTime = Date.now();
    
    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API timeout')), test.timeout);
      });

      const apiPromise = openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: "You are a mathematics assessment expert. Generate questions in valid JSON format."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: test.maxTokens,
        temperature: 0.2
      });

      const completion = await Promise.race([apiPromise, timeoutPromise]);
      const duration = Date.now() - startTime;
      
      console.log(`✅ SUCCESS: Completed in ${duration}ms`);
      console.log(`Response length: ${completion.choices[0].message.content.length}`);
      
      // Try to parse JSON
      try {
        const questions = JSON.parse(completion.choices[0].message.content);
        console.log(`✅ JSON parsed successfully: ${questions.length} questions`);
        console.log(`Sample question: ${questions[0]?.question || 'N/A'}`);
      } catch (parseError) {
        console.log(`⚠️  JSON parsing failed: ${parseError.message}`);
        console.log(`First 200 chars: ${completion.choices[0].message.content.substring(0, 200)}`);
      }
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ FAILED: ${error.message} after ${duration}ms`);
    }
  }
}

// Test OpenAI API connectivity
async function testBasicConnectivity() {
  console.log('\n=== Testing Basic OpenAI Connectivity ===');
  
  try {
    const startTime = Date.now();
    
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: "Say 'Hello, OpenAI API is working!'"
        }
      ],
      max_tokens: 50
    });
    
    const duration = Date.now() - startTime;
    console.log(`✅ Basic connectivity test passed in ${duration}ms`);
    console.log(`Response: ${completion.choices[0].message.content}`);
    
    return true;
  } catch (error) {
    console.log(`❌ Basic connectivity test failed: ${error.message}`);
    return false;
  }
}

// Test different models
async function testDifferentModels() {
  const models = ['gpt-4o-mini', 'gpt-3.5-turbo'];
  
  console.log('\n=== Testing Different Models ===');
  
  for (const model of models) {
    console.log(`\n--- Testing ${model} ---`);
    
    try {
      const startTime = Date.now();
      
      const completion = await openai.chat.completions.create({
        model: model,
        messages: [
          {
            role: "user",
            content: "Generate 2 simple math questions in JSON format: [{\"question\": \"What is 2+2?\", \"answer\": \"4\"}]"
          }
        ],
        max_tokens: 300,
        temperature: 0.2
      });
      
      const duration = Date.now() - startTime;
      console.log(`✅ ${model} responded in ${duration}ms`);
      console.log(`Response length: ${completion.choices[0].message.content.length}`);
      
    } catch (error) {
      console.log(`❌ ${model} failed: ${error.message}`);
    }
  }
}

// Main test runner
async function runAllTests() {
  console.log('🔧 OpenAI API Diagnostic Tests\n');
  
  // Test 1: Basic connectivity
  const basicWorking = await testBasicConnectivity();
  
  if (!basicWorking) {
    console.log('\n❌ Basic connectivity failed. Check your API key and network connection.');
    return;
  }
  
  // Test 2: Different models
  await testDifferentModels();
  
  // Test 3: Different parameters
  await testOpenAIWithDifferentParams();
  
  console.log('\n🎉 All diagnostic tests completed!');
}

runAllTests().catch(console.error);
