<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Calculator Test</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .viewport-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .test-button {
            background: #1547bb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .test-button:hover {
            background: #0d3a8a;
        }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Visual Calculator Interactive Question Test</h1>
        <div class="viewport-info">
            <strong>Current viewport:</strong> <span id="viewport-size"></span><br>
            <strong>Test:</strong> This simulates the actual visual calculator question from the math assessment
        </div>
        
        <button class="test-button" onclick="showCalculator()">Show Calculator Question</button>
        <button class="test-button" onclick="hideCalculator()">Hide Calculator</button>
        <button class="test-button" onclick="showDebugInfo()">Show Debug Info</button>

        <div id="debug-info" class="debug-info" style="display: none;">
            <strong>Calculator Dimensions:</strong><br>
            <span id="calc-dimensions"></span>
        </div>
        
        <div class="question-container">
            <div id="visual-calculator" class="interactive-question hidden">
                <h3>Calculate 84 - 37 step by step</h3>
                <div class="calculator-container">
                    <div class="calculator-display">
                        <div class="calculation-steps" id="calculation-steps">
                            Ready to calculate...
                        </div>
                        <div class="current-display" id="current-display">0</div>
                    </div>
                    <div class="calculator-keypad" id="calculator-keypad">
                        <!-- Buttons will be generated by JavaScript -->
                    </div>
                    <div class="calculator-controls">
                        <button id="clear-calculator-btn" class="reset-btn" type="button">
                            <span class="btn-icon">C</span>
                            <span class="btn-text">Clear</span>
                        </button>
                        <button id="step-calculator-btn" class="step-btn" type="button">
                            <span class="btn-icon">→</span>
                            <span class="btn-text">Next Step</span>
                        </button>
                    </div>
                </div>
                <p class="input-hint">Use the calculator to solve the problem step by step</p>
            </div>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            document.getElementById('viewport-size').textContent = `${width}px × ${height}px`;
        }
        
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
        
        function showCalculator() {
            const calculator = document.getElementById('visual-calculator');
            calculator.classList.remove('hidden');
            generateCalculatorButtons();
        }
        
        function hideCalculator() {
            const calculator = document.getElementById('visual-calculator');
            calculator.classList.add('hidden');
        }

        function showDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            const calcContainer = document.querySelector('.calculator-container');

            if (calcContainer) {
                const rect = calcContainer.getBoundingClientRect();
                const dimensions = `
                    Width: ${rect.width}px<br>
                    Height: ${rect.height}px<br>
                    Max-width (CSS): ${getComputedStyle(calcContainer).maxWidth}<br>
                    Max-height (CSS): ${getComputedStyle(calcContainer).maxHeight}<br>
                    Viewport: ${window.innerWidth}px × ${window.innerHeight}px
                `;
                document.getElementById('calc-dimensions').innerHTML = dimensions;
                debugDiv.style.display = 'block';
            } else {
                document.getElementById('calc-dimensions').innerHTML = 'Calculator not visible';
                debugDiv.style.display = 'block';
            }
        }
        
        function generateCalculatorButtons() {
            const keypadContainer = document.getElementById('calculator-keypad');
            keypadContainer.innerHTML = '';
            
            // Define calculator layout (same as in mathAssessment.js)
            const buttons = [
                ['C', '±', '%', '÷'],
                ['7', '8', '9', '×'],
                ['4', '5', '6', '-'],
                ['1', '2', '3', '+'],
                ['0', '.', '=', '=']
            ];
            
            buttons.forEach(row => {
                row.forEach((buttonText, index) => {
                    // Skip duplicate equals button
                    if (buttonText === '=' && index === 3) return;
                    
                    const button = document.createElement('button');
                    button.className = 'calc-btn';
                    button.textContent = buttonText;
                    button.dataset.value = buttonText;
                    
                    // Add special classes for styling
                    if (['+', '-', '×', '÷'].includes(buttonText)) {
                        button.classList.add('operator');
                    } else if (buttonText === '=') {
                        button.classList.add('equals');
                        // Keep equals button as single column to ensure visibility
                    } else if (buttonText === 'C') {
                        button.classList.add('clear');
                    } else if (buttonText === '0') {
                        button.style.gridColumn = 'span 2'; // Make 0 button wider
                    }
                    
                    // Add click handler
                    button.addEventListener('click', () => {
                        handleButtonClick(buttonText);
                    });
                    
                    keypadContainer.appendChild(button);
                });
            });
        }
        
        function handleButtonClick(buttonText) {
            console.log('Button clicked:', buttonText);
            const display = document.getElementById('current-display');
            const steps = document.getElementById('calculation-steps');
            
            // Simple display update for testing
            if (buttonText === 'C') {
                display.textContent = '0';
                steps.textContent = 'Ready to calculate...';
            } else if (buttonText === '=') {
                // Simple calculation for 84 - 37
                if (display.textContent.includes('84 - 37')) {
                    display.textContent = '47';
                    steps.innerHTML = 'Step 1: Enter 84<br>Step 2: Press - (minus)<br>Step 3: Enter 37<br>Step 4: Press = (equals)<br>Result: 47';
                }
            } else {
                // Update display with button press
                if (display.textContent === '0') {
                    display.textContent = buttonText;
                } else {
                    display.textContent += buttonText;
                }
            }
        }
    </script>
</body>
</html>
