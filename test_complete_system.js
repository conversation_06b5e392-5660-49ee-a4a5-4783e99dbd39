const http = require('http');

// Test the complete mathematics assessment system with the fixes
async function testCompleteMathematicsAssessment() {
  console.log('🧮 Testing Complete Mathematics Assessment System\n');
  
  const postData = JSON.stringify({
    email: '<EMAIL>',
    level: 'Level1',
    studentLevel: 'adult-learner'
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/math-assessments/start',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  console.log('📡 Making request to mathematics assessment API...');
  const startTime = Date.now();

  try {
    const response = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          const duration = Date.now() - startTime;
          
          if (res.statusCode === 200) {
            try {
              const result = JSON.parse(data);
              resolve({ success: true, data: result, duration, statusCode: res.statusCode });
            } catch (parseError) {
              reject({ success: false, error: 'JSON parse error', details: parseError.message, duration });
            }
          } else {
            reject({ success: false, error: `HTTP ${res.statusCode}`, details: data, duration });
          }
        });
      });

      req.on('error', (error) => {
        const duration = Date.now() - startTime;
        reject({ success: false, error: 'Request error', details: error.message, duration });
      });

      req.on('timeout', () => {
        const duration = Date.now() - startTime;
        reject({ success: false, error: 'Request timeout', details: 'Request timed out', duration });
      });

      req.setTimeout(60000); // 60 second timeout for the HTTP request
      req.write(postData);
      req.end();
    });

    // Success!
    console.log(`✅ Assessment API responded in ${response.duration}ms`);
    console.log(`📊 Assessment ID: ${response.data.assessmentId}`);
    console.log(`📝 Questions generated: ${response.data.questions.length}`);
    console.log(`⏱️  Time limit: ${response.data.timeLimit} minutes`);
    console.log(`🎯 Passing score: ${response.data.passingScore}/${response.data.maxScore}`);
    
    // Analyze the questions in detail
    const questions = response.data.questions;
    if (questions && questions.length > 0) {
      console.log('\n📋 Detailed Question Analysis:');
      console.log('='.repeat(80));
      
      let measurementCount = 0;
      let fractionCount = 0;
      let multipleChoiceCount = 0;
      let numericCount = 0;
      let shortAnswerCount = 0;
      let validQuestions = 0;
      let issuesFound = [];
      
      questions.forEach((q, index) => {
        console.log(`\nQuestion ${index + 1}:`);
        console.log(`- ID: ${q.id}`);
        console.log(`- Type: "${q.type}"`);
        console.log(`- Topic: ${q.topic}`);
        console.log(`- Question: ${q.question}`);
        console.log(`- Correct Answer: ${q.correctAnswer}`);
        console.log(`- Points: ${q.points}`);
        
        // Count question types
        if (q.type === 'multiple-choice') {
          console.log(`- Options: [${q.options ? q.options.join(', ') : 'MISSING'}]`);
          multipleChoiceCount++;
          
          if (!q.options || !Array.isArray(q.options) || q.options.length !== 4) {
            issuesFound.push(`Question ${index + 1}: Multiple choice missing 4 options`);
          }
        } else if (q.type === 'numeric') {
          numericCount++;
          if (q.options) {
            issuesFound.push(`Question ${index + 1}: Numeric question should not have options`);
          }
        } else if (q.type === 'short-answer') {
          shortAnswerCount++;
          if (q.options) {
            issuesFound.push(`Question ${index + 1}: Short answer question should not have options`);
          }
        } else {
          issuesFound.push(`Question ${index + 1}: Unknown question type "${q.type}"`);
        }
        
        // Count topics
        if (q.topic === 'measurement') measurementCount++;
        if (q.topic === 'fractions') fractionCount++;
        
        // Validate required fields
        const hasAllFields = q.id && q.type && q.topic && q.question && q.correctAnswer && q.points;
        if (hasAllFields) {
          validQuestions++;
        } else {
          issuesFound.push(`Question ${index + 1}: Missing required fields`);
        }
        
        // Frontend rendering simulation
        console.log('- Frontend rendering:');
        if (q.type === 'multiple-choice') {
          if (q.options && Array.isArray(q.options) && q.options.length === 4) {
            console.log('  ✅ Will render multiple choice with 4 options');
          } else {
            console.log('  ❌ Multiple choice will fail - missing proper options');
          }
        } else if (q.type === 'numeric') {
          console.log('  ✅ Will render numeric input field');
        } else if (q.type === 'short-answer') {
          console.log('  ✅ Will render short answer input field');
        } else {
          console.log(`  ⚠️  Unknown type "${q.type}" - will default to short answer`);
        }
      });
      
      // Summary report
      console.log('\n📈 Assessment Summary:');
      console.log('='.repeat(60));
      console.log(`Total questions: ${questions.length}`);
      console.log(`Valid questions: ${validQuestions}/${questions.length}`);
      console.log(`\nQuestion type distribution:`);
      console.log(`  - Multiple choice: ${multipleChoiceCount} (${(multipleChoiceCount/questions.length*100).toFixed(1)}%)`);
      console.log(`  - Numeric: ${numericCount} (${(numericCount/questions.length*100).toFixed(1)}%)`);
      console.log(`  - Short answer: ${shortAnswerCount} (${(shortAnswerCount/questions.length*100).toFixed(1)}%)`);
      console.log(`\nTopic coverage:`);
      console.log(`  - Measurement questions: ${measurementCount}`);
      console.log(`  - Fraction questions: ${fractionCount}`);
      console.log(`  - Other topics: ${questions.length - measurementCount - fractionCount}`);
      
      // Quality assessment
      console.log('\n🎯 Quality Assessment:');
      const hasVariety = multipleChoiceCount > 0 && (numericCount > 0 || shortAnswerCount > 0);
      const hasMeasurement = measurementCount > 0;
      const hasFractions = fractionCount > 0;
      const allValid = validQuestions === questions.length;
      const noIssues = issuesFound.length === 0;
      
      console.log(`✅ Question type variety: ${hasVariety ? 'PASS' : 'FAIL'}`);
      console.log(`✅ Measurement questions included: ${hasMeasurement ? 'PASS' : 'FAIL'}`);
      console.log(`✅ Fraction questions included: ${hasFractions ? 'PASS' : 'FAIL'}`);
      console.log(`✅ All questions valid: ${allValid ? 'PASS' : 'FAIL'}`);
      console.log(`✅ No structural issues: ${noIssues ? 'PASS' : 'FAIL'}`);
      
      if (issuesFound.length > 0) {
        console.log('\n⚠️  Issues found:');
        issuesFound.forEach(issue => console.log(`  - ${issue}`));
      }
      
      // Final verdict
      if (hasVariety && hasMeasurement && hasFractions && allValid && noIssues) {
        console.log('\n🎉 EXCELLENT: All requirements met perfectly!');
        console.log('The mathematics assessment system is working correctly.');
      } else if (validQuestions > questions.length * 0.8) {
        console.log('\n✅ GOOD: System is functional with minor issues.');
        console.log('Most questions will render correctly in the frontend.');
      } else {
        console.log('\n⚠️  NEEDS IMPROVEMENT: Significant issues detected.');
        console.log('Some questions may not render properly in the frontend.');
      }
      
      // Show sample questions for manual verification
      console.log('\n📝 Sample Questions for Manual Verification:');
      console.log('='.repeat(60));
      
      const sampleQuestions = questions.slice(0, 3);
      sampleQuestions.forEach((q, index) => {
        console.log(`\nSample ${index + 1}: ${q.question}`);
        console.log(`Type: ${q.type}`);
        if (q.type === 'multiple-choice') {
          console.log(`Options: ${q.options ? q.options.join(' | ') : 'NONE'}`);
        }
        console.log(`Answer: ${q.correctAnswer}`);
      });
      
    } else {
      console.log('❌ No questions received from API');
    }
    
  } catch (error) {
    console.log(`❌ Assessment failed after ${error.duration}ms`);
    console.log(`Error: ${error.error}`);
    console.log(`Details: ${error.details}`);
  }
}

// Check server status first
async function checkServerStatus() {
  try {
    await new Promise((resolve, reject) => {
      const req = http.request({
        hostname: 'localhost',
        port: 3000,
        path: '/api/math-assessments/performance',
        method: 'GET'
      }, (res) => {
        res.on('data', () => {});
        res.on('end', () => {
          if (res.statusCode === 200) {
            resolve();
          } else {
            reject(new Error(`Server returned ${res.statusCode}`));
          }
        });
      });
      
      req.on('error', reject);
      req.setTimeout(5000);
      req.end();
    });
    
    console.log('✅ Server is running and accessible');
    return true;
  } catch (error) {
    console.log('❌ Server is not accessible:', error.message);
    console.log('Please start the server with: node server.js');
    return false;
  }
}

// Main test runner
async function runCompleteTest() {
  console.log('🔧 Complete Mathematics Assessment System Test\n');
  console.log('This test verifies the complete end-to-end functionality:');
  console.log('✓ API generates questions with correct types and formats');
  console.log('✓ Measurement and fraction questions are included');
  console.log('✓ All questions have proper input mechanisms');
  console.log('✓ Frontend compatibility is maintained');
  console.log('✓ Question normalization works correctly\n');
  
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    return;
  }
  
  await testCompleteMathematicsAssessment();
  
  console.log('\n🎉 Complete system test finished!');
}

runCompleteTest().catch(console.error);
