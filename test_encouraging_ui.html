<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mathematics Assessment - Encouraging UI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        .achievement-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .achievement-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .achievement-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .achievement-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1f2937;
        }
        .achievement-score {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 15px;
        }
        .mock-status-badge {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            color: white;
            margin-bottom: 15px;
        }
        .mock-status-badge.passed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .mock-status-badge.excellent-progress {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        .mock-status-badge.good-progress {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }
        .mock-status-badge.steady-progress {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .mock-status-badge.foundation-building {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        }
        .mock-status-badge.getting-started {
            background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
        }
        .mock-message {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            color: #374151;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            background: #fee2e2;
            border: 2px solid #ef4444;
        }
        .after {
            background: #d1fae5;
            border: 2px solid #10b981;
        }
        .comparison-title {
            font-weight: 600;
            margin-bottom: 10px;
        }
        .old-status {
            background: #fee2e2;
            color: #991b1b;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-weight: 600;
            display: inline-block;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Mathematics Assessment - Encouraging UI Enhancement</h1>
    
    <div class="test-section">
        <h2>Before vs After Comparison</h2>
        <p>See the improvement from discouraging "Not Passed" messaging to encouraging achievement levels.</p>
        
        <div class="comparison-grid">
            <div class="comparison-item before">
                <div class="comparison-title">❌ Before (Discouraging)</div>
                <div style="background: #3b82f6; color: white; padding: 1rem; border-radius: 8px; margin: 10px 0;">
                    <strong>Your Score: 18 / 44</strong>
                </div>
                <div class="old-status">Not Passed</div>
                <p style="margin-top: 10px; font-size: 0.9rem;">
                    • Poor text contrast on blue background<br>
                    • Negative, discouraging messaging<br>
                    • No context about achievement level<br>
                    • Focus on failure rather than progress
                </p>
            </div>
            
            <div class="comparison-item after">
                <div class="comparison-title">✅ After (Encouraging)</div>
                <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 1rem; border-radius: 8px; margin: 10px 0; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);">
                    <strong style="color: #e0e7ff;">Your Score:</strong> <span style="font-size: 1.5rem; color: white;">18</span> <span style="color: #e0e7ff;">/ 44</span>
                </div>
                <div class="mock-status-badge good-progress">Good Progress Achievement</div>
                <div class="mock-message">
                    🌟 Great job! You're making solid progress in your mathematical journey.
                </div>
                <p style="margin-top: 10px; font-size: 0.9rem;">
                    • High contrast white text on blue background<br>
                    • Positive, encouraging achievement levels<br>
                    • Clear progress indication<br>
                    • Motivational messaging with next steps
                </p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Achievement Level Preview</h2>
        <p>Preview all the different achievement levels and their encouraging messages:</p>
        
        <div class="achievement-preview">
            <div class="achievement-card">
                <div class="achievement-title">Level Complete</div>
                <div class="achievement-score">Score: 35+ / 44 (Passed)</div>
                <div class="mock-status-badge passed">Entry Level Complete!</div>
                <div class="mock-message">🎉 Congratulations! You've successfully completed this level and can progress to the next!</div>
            </div>
            
            <div class="achievement-card">
                <div class="achievement-title">Excellent Progress</div>
                <div class="achievement-score">Score: 25-34 / 44 (80%+)</div>
                <div class="mock-status-badge excellent-progress">Excellent Progress Achievement</div>
                <div class="mock-message">🌟 Outstanding work! You're demonstrating strong mathematical skills.</div>
            </div>
            
            <div class="achievement-card">
                <div class="achievement-title">Good Progress</div>
                <div class="achievement-score">Score: 18-24 / 44 (60-79%)</div>
                <div class="mock-status-badge good-progress">Good Progress Achievement</div>
                <div class="mock-message">🌟 Great job! You're making solid progress in your mathematical journey.</div>
            </div>
            
            <div class="achievement-card">
                <div class="achievement-title">Steady Progress</div>
                <div class="achievement-score">Score: 12-17 / 44 (40-59%)</div>
                <div class="mock-status-badge steady-progress">Steady Progress Achievement</div>
                <div class="mock-message">🌟 Well done! You're building a solid foundation in mathematics.</div>
            </div>
            
            <div class="achievement-card">
                <div class="achievement-title">Foundation Building</div>
                <div class="achievement-score">Score: 6-11 / 44 (20-39%)</div>
                <div class="mock-status-badge foundation-building">Foundation Building Achievement</div>
                <div class="mock-message">🌟 Good start! You're developing important mathematical foundations.</div>
            </div>
            
            <div class="achievement-card">
                <div class="achievement-title">Getting Started</div>
                <div class="achievement-score">Score: 0-5 / 44 (0-19%)</div>
                <div class="mock-status-badge getting-started">Getting Started Achievement</div>
                <div class="mock-message">🌟 You've taken the first step in your mathematical journey!</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Interactive Testing</h2>
        <p>Test the new encouraging UI with different score scenarios:</p>
        
        <button class="test-button" onclick="testAchievementLevel(35, 'passed')">Test Passed (35/44)</button>
        <button class="test-button" onclick="testAchievementLevel(28, 'excellent')">Test Excellent (28/44)</button>
        <button class="test-button" onclick="testAchievementLevel(20, 'good')">Test Good Progress (20/44)</button>
        <button class="test-button" onclick="testAchievementLevel(15, 'steady')">Test Steady Progress (15/44)</button>
        <button class="test-button" onclick="testAchievementLevel(8, 'foundation')">Test Foundation (8/44)</button>
        <button class="test-button" onclick="testAchievementLevel(3, 'getting-started')">Test Getting Started (3/44)</button>
        <button class="test-button" onclick="openMathAssessment()">Open Live Assessment</button>
        
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Live Assessment Preview</h2>
        <p>Preview the actual mathematics assessment with the new encouraging UI:</p>
        
        <iframe id="assessment-frame" src="/math.html"></iframe>
    </div>

    <div class="test-section">
        <h2>Accessibility Improvements</h2>
        <ul>
            <li>✅ <strong>High Contrast Text:</strong> White text on blue backgrounds for WCAG compliance</li>
            <li>✅ <strong>Positive Messaging:</strong> Achievement levels instead of "Not Passed"</li>
            <li>✅ <strong>Clear Visual Hierarchy:</strong> Gradient backgrounds and proper spacing</li>
            <li>✅ <strong>Encouraging Feedback:</strong> Motivational messages with progress indicators</li>
            <li>✅ <strong>Responsive Design:</strong> Adapts to all screen sizes</li>
            <li>✅ <strong>Animation Effects:</strong> Subtle sparkle animation for engagement</li>
        </ul>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const container = document.getElementById('test-results');
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
            result.scrollIntoView({ behavior: 'smooth' });
        }

        function testAchievementLevel(score, level) {
            addResult(`🧪 Testing ${level} achievement level with score ${score}/44`, 'info');
            
            const frame = document.getElementById('assessment-frame');
            const frameWindow = frame.contentWindow;
            
            if (frameWindow.mathAssessment) {
                // Create mock results for testing
                const mockResults = {
                    score: score,
                    maxScore: 44,
                    passed: score >= 24,
                    topicBreakdown: {
                        arithmetic: Math.floor(score * 0.3),
                        algebra: Math.floor(score * 0.2),
                        geometry: Math.floor(score * 0.15),
                        statistics: Math.floor(score * 0.15),
                        problemSolving: Math.floor(score * 0.1),
                        measurement: Math.floor(score * 0.1)
                    },
                    feedback: {
                        overall: `Test feedback for ${level} achievement level with score ${score}/44.`
                    },
                    strengths: [`Strong performance in areas related to ${level} level`],
                    improvements: [`Continue building skills for ${level} progression`],
                    placementRecommendation: {
                        level: level,
                        reasoning: `Based on ${score}/44 score, ${level} level recommended.`,
                        nextSteps: [`Continue practicing at ${level} level`],
                        courseRecommendations: [`${level} Mathematics Course`]
                    }
                };
                
                frameWindow.mathAssessment.showResults(mockResults);
                addResult(`✅ Displayed ${level} achievement level in assessment frame`, 'success');
            } else {
                addResult(`⚠️ Assessment not loaded in frame. Try opening the live assessment first.`, 'warning');
            }
        }

        function openMathAssessment() {
            window.open('/math.html', '_blank');
            addResult('🚀 Opened mathematics assessment in new tab', 'info');
        }

        // Initial message
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎨 Encouraging UI Test Suite Ready', 'info');
            addResult('📋 Use the buttons above to test different achievement levels', 'info');
            addResult('🌟 Notice how each level provides positive, encouraging feedback', 'success');
        });
    </script>
</body>
</html>
