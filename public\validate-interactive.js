/**
 * Interactive Questions Validation Script
 * Tests critical functionality of all 8 interactive question types
 */

class InteractiveQuestionValidator {
  constructor() {
    this.results = [];
    this.currentTest = null;
  }

  /**
   * Run all validation tests
   */
  async runAllTests() {
    console.log('🧪 Starting Interactive Questions Validation...');
    
    const tests = [
      { name: 'Data Library', test: () => this.testDataLibrary() },
      { name: 'Step-by-Step Input', test: () => this.testStepByStepInput() },
      { name: 'Drag-Drop Functionality', test: () => this.testDragDropFunctionality() },
      { name: 'Answer Collection', test: () => this.testAnswerCollection() },
      { name: 'Visual Indicators', test: () => this.testVisualIndicators() },
      { name: 'Accessibility', test: () => this.testAccessibility() },
      { name: 'Mobile Compatibility', test: () => this.testMobileCompatibility() },
      { name: 'Integration', test: () => this.testIntegration() }
    ];

    for (const test of tests) {
      try {
        this.currentTest = test.name;
        console.log(`\n🔍 Testing: ${test.name}`);
        await test.test();
        this.logResult(test.name, 'PASS', 'All checks passed');
      } catch (error) {
        this.logResult(test.name, 'FAIL', error.message);
        console.error(`❌ ${test.name} failed:`, error);
      }
    }

    this.printSummary();
  }

  /**
   * Test data library availability and structure
   */
  testDataLibrary() {
    // Check if data library exists
    if (typeof window.InteractiveQuestionData === 'undefined') {
      throw new Error('InteractiveQuestionData not loaded');
    }

    // Check statistics
    const stats = window.InteractiveQuestionData.getStatistics();
    if (stats.totalQuestions === 0) {
      throw new Error('No interactive questions found in data library');
    }

    // Check all question types exist
    const requiredTypes = ['number-line', 'drag-drop', 'visual-calculator', 'number-bonds', 'step-by-step',
                          'coordinate-plot', 'ratio-slider', 'equation-builder', 'pattern-completion'];
    
    for (const type of requiredTypes) {
      if (!stats.byType[type] || stats.byType[type] === 0) {
        throw new Error(`No questions found for type: ${type}`);
      }
    }

    console.log('✅ Data library loaded with', stats.totalQuestions, 'questions');
  }

  /**
   * Test step-by-step input mechanisms
   */
  testStepByStepInput() {
    // Check if required elements exist
    const stepInput = document.getElementById('step-answer');
    const checkBtn = document.getElementById('check-step-btn');
    
    if (!stepInput) throw new Error('Step input element not found');
    if (!checkBtn) throw new Error('Check step button not found');

    // Test input functionality
    stepInput.value = 'test answer';
    const inputEvent = new Event('input', { bubbles: true });
    stepInput.dispatchEvent(inputEvent);

    // Test button click
    const clickEvent = new Event('click', { bubbles: true });
    checkBtn.dispatchEvent(clickEvent);

    console.log('✅ Step-by-step input elements functional');
  }

  /**
   * Test drag-drop functionality
   */
  testDragDropFunctionality() {
    // Check if interact.js is loaded
    if (typeof interact === 'undefined') {
      throw new Error('Interact.js library not loaded');
    }

    // Check if drag-drop elements exist
    const draggableItems = document.querySelectorAll('.draggable-item');
    const dropZones = document.querySelectorAll('.drop-zone');

    if (draggableItems.length === 0) {
      console.log('⚠️ No draggable items found (may be normal if not currently testing drag-drop)');
    }

    if (dropZones.length === 0) {
      console.log('⚠️ No drop zones found (may be normal if not currently testing drag-drop)');
    }

    console.log('✅ Drag-drop infrastructure available');
  }

  /**
   * Test answer collection methods
   */
  testAnswerCollection() {
    // Test if mathAssessment object exists
    if (typeof window.mathAssessment === 'undefined') {
      throw new Error('mathAssessment object not found');
    }

    // Test answer collection methods exist
    const requiredMethods = [
      'getDragDropAnswer',
      'getStepByStepAnswer',
      'getVisualCalculatorAnswer',
      'getNumberBondsAnswer',
      'getCoordinatePlottingAnswer',
      'getRatioSlidersAnswer',
      'getEquationBuilderAnswer',
      'getPatternCompletionAnswer'
    ];

    for (const method of requiredMethods) {
      if (typeof window.mathAssessment[method] !== 'function') {
        throw new Error(`Answer collection method ${method} not found`);
      }
    }

    // Test that methods return valid JSON
    try {
      const testAnswer = window.mathAssessment.getDragDropAnswer();
      JSON.parse(testAnswer);
    } catch (error) {
      throw new Error('Answer collection methods do not return valid JSON');
    }

    console.log('✅ Answer collection methods available and functional');
  }

  /**
   * Test visual indicators
   */
  testVisualIndicators() {
    // Check if CSS classes exist
    const testElement = document.createElement('div');
    testElement.className = 'completion-indicator';
    document.body.appendChild(testElement);

    const styles = window.getComputedStyle(testElement);
    if (styles.position !== 'absolute') {
      console.log('⚠️ Completion indicator styles may not be loaded properly');
    }

    document.body.removeChild(testElement);

    // Check if status indicator methods exist
    if (typeof window.mathAssessment.showCompletionIndicator !== 'function') {
      throw new Error('showCompletionIndicator method not found');
    }

    console.log('✅ Visual indicator system available');
  }

  /**
   * Test accessibility features
   */
  testAccessibility() {
    // Check for ARIA attributes on interactive elements
    const interactiveElements = document.querySelectorAll('[role], [aria-label], [tabindex]');
    
    if (interactiveElements.length === 0) {
      console.log('⚠️ No ARIA attributes found (may be normal if no interactive questions loaded)');
    }

    // Check for keyboard navigation support
    const focusableElements = document.querySelectorAll('[tabindex="0"]');
    
    console.log('✅ Accessibility features present');
  }

  /**
   * Test mobile compatibility
   */
  testMobileCompatibility() {
    // Check viewport meta tag
    const viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      throw new Error('Viewport meta tag not found');
    }

    // Check for touch event support
    const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    console.log('✅ Mobile compatibility features present');
  }

  /**
   * Test integration with assessment flow
   */
  testIntegration() {
    // Check if next button exists
    const nextBtn = document.getElementById('next-question-btn');
    if (!nextBtn) {
      throw new Error('Next question button not found');
    }

    // Check if question mixing function exists
    if (typeof window.mathAssessment.mixQuestionsWithInteractive !== 'function') {
      throw new Error('Question mixing function not found');
    }

    // Check if cleanup functions exist
    if (typeof window.mathAssessment.cleanupInteractiveQuestions !== 'function') {
      throw new Error('Cleanup function not found');
    }

    console.log('✅ Integration with assessment flow verified');
  }

  /**
   * Log test result
   */
  logResult(testName, status, message) {
    this.results.push({
      test: testName,
      status: status,
      message: message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Print validation summary
   */
  printSummary() {
    console.log('\n📊 VALIDATION SUMMARY');
    console.log('='.repeat(50));
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    
    console.log(`Total Tests: ${this.results.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.filter(r => r.status === 'FAIL').forEach(result => {
        console.log(`  • ${result.test}: ${result.message}`);
      });
    }
    
    if (passed === this.results.length) {
      console.log('\n🎉 ALL TESTS PASSED! Interactive questions are ready for production.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review and fix issues before deployment.');
    }

    // Return results for programmatic access
    return {
      total: this.results.length,
      passed: passed,
      failed: failed,
      successRate: (passed / this.results.length) * 100,
      results: this.results
    };
  }
}

// Auto-run validation when script loads
document.addEventListener('DOMContentLoaded', function() {
  // Wait a bit for other scripts to load
  setTimeout(() => {
    const validator = new InteractiveQuestionValidator();
    window.interactiveValidator = validator;
    
    // Run validation automatically
    validator.runAllTests().then(summary => {
      // Store results globally for access
      window.validationResults = summary;
    });
  }, 1000);
});

// Export for manual testing
window.InteractiveQuestionValidator = InteractiveQuestionValidator;
