/**
 * Test script to validate the JSON parsing fix for mathematics assessment
 * This script tests the specific issue where valid JSON was being corrupted by over-escaping
 */

// Simulate the fixed cleanCommonJSONIssues function
function cleanCommonJSONIssues(text) {
  let cleaned = text;
  
  console.log('🧹 Starting JSON cleaning process...');
  console.log(`Original text sample: ${text.substring(0, 200)}...`);
  
  // CRITICAL FIX: Only apply cleaning if we detect actual issues
  // First, try to parse as-is to see if it's already valid JSON
  try {
    JSON.parse(cleaned);
    console.log('✅ JSON is already valid - skipping aggressive cleaning');
    return cleaned; // Return original if it's already valid
  } catch (e) {
    console.log(`🔧 JSON needs cleaning: ${e.message.substring(0, 100)}`);
  }
  
  // Only apply minimal, safe cleaning operations
  
  // 1. Remove markdown code blocks if present
  cleaned = cleaned.replace(/```json\s*/g, '').replace(/```\s*/g, '');
  cleaned = cleaned.replace(/`/g, '');
  
  // 2. Fix trailing commas in arrays and objects (safe operation)
  cleaned = cleaned.replace(/,(\s*[}\]])/g, '$1');
  
  // 3. Fix missing commas between array elements (safe operation)
  cleaned = cleaned.replace(/}(\s*){/g, '},$1{');
  
  // 4. Fix malformed decimal numbers (safe operation)
  cleaned = cleaned.replace(/(\d+)\.(\s*[,}])/g, '$1.0$2');
  
  // 5. Remove non-printable characters (safe operation)
  cleaned = cleaned.replace(/[\x00-\x1F\x7F-\x9F]/g, '');
  
  // 6. TARGETED currency symbol fix - only within string values, not structural quotes
  // This regex specifically targets £ symbols within quoted strings
  cleaned = cleaned.replace(/"([^"]*£[^"]*)"/g, (match, content) => {
    return `"${content.replace(/£/g, '\\u00A3')}"`;
  });
  
  // 7. COMPREHENSIVE single quote fix - convert all single quotes to double quotes
  // This is a more systematic approach that handles all cases
  cleaned = cleaned.replace(/'/g, '"');
  
  console.log(`Cleaned text sample: ${cleaned.substring(0, 200)}...`);
  console.log(`JSON cleaning applied: ${text.length} → ${cleaned.length} characters`);
  
  return cleaned;
}

// Simulate the enhanced parsing function
function parseAIQuestionsResponse(questionsText, level) {
  try {
    console.log(`\n🔍 Parsing AI response for ${level} level (${questionsText.length} characters)`);
    console.log(`Response sample: ${questionsText.substring(0, 300)}...`);
    
    let questions;

    // Strategy 1: Try parsing original text first (AI might have generated valid JSON)
    try {
      questions = JSON.parse(questionsText);
      if (Array.isArray(questions)) {
        console.log(`✅ Original JSON parse successful: ${questions.length} questions`);
        return questions;
      }
    } catch (e) {
      console.log(`Strategy 1 (original) failed: ${e.message.substring(0, 100)}`);
    }

    // Strategy 2: Try with minimal cleaning (remove markdown only)
    try {
      let minimalCleaned = questionsText;
      minimalCleaned = minimalCleaned.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      minimalCleaned = minimalCleaned.replace(/`/g, '');
      
      questions = JSON.parse(minimalCleaned);
      if (Array.isArray(questions)) {
        console.log(`✅ Minimal cleaning parse successful: ${questions.length} questions`);
        return questions;
      }
    } catch (e) {
      console.log(`Strategy 2 (minimal cleaning) failed: ${e.message.substring(0, 100)}`);
    }

    // Strategy 3: Apply full cleaning only if minimal cleaning failed
    try {
      const cleanedText = cleanCommonJSONIssues(questionsText);
      questions = JSON.parse(cleanedText);
      if (Array.isArray(questions)) {
        console.log(`✅ Full cleaning parse successful: ${questions.length} questions`);
        return questions;
      }
    } catch (e) {
      console.log(`Strategy 3 (full cleaning) failed: ${e.message.substring(0, 100)}`);
    }

    console.error('❌ All parsing strategies failed');
    return null;

  } catch (parseError) {
    console.error('Error parsing AI questions response:', parseError);
    return null;
  }
}

// Test cases based on the actual log errors
const testCases = [
  {
    name: "Valid JSON (should not be corrupted)",
    input: `[
  {
    "id": 1,
    "type": "multiple-choice",
    "topic": "arithmetic",
    "question": "What is 25 + 37?",
    "options": ["52", "62", "72", "82"],
    "correctAnswer": "62",
    "points": 2,
    "explanation": "25 + 37 = 62"
  }
]`,
    expectedSuccess: true
  },
  {
    name: "JSON with currency symbols",
    input: `[
  {
    "id": 1,
    "type": "multiple-choice",
    "topic": "arithmetic",
    "question": "A shop sells apples for £1.20 per kilogram. How much would 3 kilograms cost?",
    "options": ["£2.60", "£3.60", "£4.20", "£3.20"],
    "correctAnswer": "£3.60",
    "points": 2,
    "explanation": "3 × £1.20 = £3.60"
  }
]`,
    expectedSuccess: true
  },
  {
    name: "JSON with markdown wrapper",
    input: `\`\`\`json
[
  {
    "id": 1,
    "type": "multiple-choice",
    "topic": "arithmetic",
    "question": "What is 15 × 4?",
    "options": ["50", "60", "70", "80"],
    "correctAnswer": "60",
    "points": 2,
    "explanation": "15 × 4 = 60"
  }
]
\`\`\``,
    expectedSuccess: true
  },
  {
    name: "JSON with single quotes (needs fixing)",
    input: `[
  {
    'id': 1,
    'type': 'multiple-choice',
    'topic': 'arithmetic',
    'question': 'What is 10 + 5?',
    'options': ['15', '20', '25', '30'],
    'correctAnswer': '15',
    'points': 2,
    'explanation': '10 + 5 = 15'
  }
]`,
    expectedSuccess: true
  },
  {
    name: "JSON with trailing commas (needs fixing)",
    input: `[
  {
    "id": 1,
    "type": "multiple-choice",
    "topic": "arithmetic",
    "question": "What is 8 × 7?",
    "options": ["54", "56", "58", "60"],
    "correctAnswer": "56",
    "points": 2,
    "explanation": "8 × 7 = 56",
  }
]`,
    expectedSuccess: true
  }
];

// Run tests
function runTests() {
  console.log('🧪 Testing JSON Parsing Fix for Mathematics Assessment');
  console.log('====================================================\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📝 Test ${index + 1}: ${testCase.name}`);
    console.log('='.repeat(50));
    
    const result = parseAIQuestionsResponse(testCase.input, 'Entry');
    const success = result !== null && Array.isArray(result) && result.length > 0;
    
    if (success === testCase.expectedSuccess) {
      console.log(`✅ Test PASSED: ${success ? 'Successfully parsed' : 'Expected failure'}`);
      passedTests++;
    } else {
      console.log(`❌ Test FAILED: Expected ${testCase.expectedSuccess ? 'success' : 'failure'}, got ${success ? 'success' : 'failure'}`);
    }
    
    if (result && result.length > 0) {
      console.log(`📊 Parsed ${result.length} questions successfully`);
      console.log(`Sample question: ${result[0].question.substring(0, 50)}...`);
    }
  });
  
  console.log('\n📋 Test Summary');
  console.log('================');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! JSON parsing fix is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Review the implementation.');
  }
}

// Run the tests
runTests();
