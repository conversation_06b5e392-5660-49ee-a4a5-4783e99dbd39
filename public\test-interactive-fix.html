<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Questions Fix Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: #f5f5f5;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        #next-question-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.enabled {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disabled {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Interactive Questions Fix Test</h1>
        <p>This page tests the fixes for the mathematics assessment interactive questions where users could get stuck.</p>
        
        <div id="status" class="status disabled">Next Button Status: DISABLED</div>
        <button id="next-question-btn" disabled>Next Question</button>
        
        <div class="test-section">
            <h3>Test 1: Drag and Drop</h3>
            <p>Try dragging an item to a drop zone. The next button should enable immediately.</p>
            <div id="drag-drop-matching" class="drag-drop-matching">
                <div id="draggable-items" class="draggable-items">
                    <div class="draggable-item" data-item-id="item1" draggable="true">Item 1</div>
                    <div class="draggable-item" data-item-id="item2" draggable="true">Item 2</div>
                </div>
                <div id="drop-zones" class="drop-zones">
                    <div class="drop-zone" data-zone-id="zone1">
                        <div class="drop-zone-content">Drop here</div>
                    </div>
                    <div class="drop-zone" data-zone-id="zone2">
                        <div class="drop-zone-content">Drop here</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Number Line</h3>
            <p>Try clicking or dragging on the number line. The next button should enable immediately.</p>
            <div id="number-line-slider" class="number-line-slider">
                <div id="number-line-track" class="number-line-track">
                    <div id="number-line-handle" class="number-line-handle"></div>
                </div>
                <div id="number-line-value" class="number-line-value">0</div>
                <div id="number-line-labels" class="number-line-labels"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Area Model</h3>
            <p>Try clicking on a fraction segment or shape. The next button should enable immediately.</p>
            <div id="area-models" class="area-models">
                <div id="fraction-bars" class="fraction-bars">
                    <div class="fraction-bar">
                        <div class="fraction-segment" data-bar-index="0" data-segment-index="0"></div>
                        <div class="fraction-segment" data-bar-index="0" data-segment-index="1"></div>
                        <div class="fraction-segment" data-bar-index="0" data-segment-index="2"></div>
                        <div class="fraction-segment" data-bar-index="0" data-segment-index="3"></div>
                    </div>
                </div>
                <div id="geometric-shapes" class="geometric-shapes">
                    <div class="geometric-shape" data-shape-index="0">Shape 1</div>
                    <div class="geometric-shape" data-shape-index="1">Shape 2</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test 4: Fallback Timer</h3>
            <p>Wait 30 seconds without interacting. The next button should auto-enable with a help message.</p>
            <button onclick="testFallback()">Start Fallback Test</button>
        </div>
    </div>

    <script src="mathAssessment.js"></script>
    <script>
        // Initialize test environment
        const mathAssessment = new MathAssessment();
        mathAssessment.init();
        
        // Mock question data for testing
        const mockQuestion = {
            type: 'drag-drop',
            dragDropConfig: {
                items: [
                    { id: 'item1', text: 'Item 1' },
                    { id: 'item2', text: 'Item 2' }
                ],
                zones: [
                    { id: 'zone1', label: 'Zone 1' },
                    { id: 'zone2', label: 'Zone 2' }
                ]
            }
        };
        
        // Initialize drag-drop state
        mathAssessment.dragDropState = {
            matches: new Map(),
            items: mockQuestion.dragDropConfig.items,
            zones: mockQuestion.dragDropConfig.zones
        };
        
        // Initialize area model state
        mathAssessment.areaModelState = {
            shadedSegments: new Set(),
            shadedShapes: new Set()
        };
        
        // Set up event listeners for testing
        mathAssessment.setupDragDropEvents();
        
        // Number line setup
        const track = document.getElementById('number-line-track');
        const handle = document.getElementById('number-line-handle');
        const valueDisplay = document.getElementById('number-line-value');
        mathAssessment.setupNumberLineEvents(track, handle, valueDisplay, -10, 10, 1, true);
        
        // Area model setup
        document.querySelectorAll('.fraction-segment').forEach((segment, index) => {
            segment.addEventListener('click', () => {
                mathAssessment.toggleFractionSegment(0, index);
            });
        });
        
        document.querySelectorAll('.geometric-shape').forEach((shape, index) => {
            shape.addEventListener('click', () => {
                mathAssessment.toggleGeometricShape(index);
            });
        });
        
        // Monitor button state changes
        const statusDiv = document.getElementById('status');
        const nextBtn = document.getElementById('next-question-btn');
        
        function updateStatus() {
            if (nextBtn.disabled) {
                statusDiv.textContent = 'Next Button Status: DISABLED';
                statusDiv.className = 'status disabled';
            } else {
                statusDiv.textContent = 'Next Button Status: ENABLED';
                statusDiv.className = 'status enabled';
            }
        }
        
        // Watch for button state changes
        const observer = new MutationObserver(updateStatus);
        observer.observe(nextBtn, { attributes: true, attributeFilter: ['disabled'] });
        
        // Test fallback functionality
        function testFallback() {
            nextBtn.disabled = true;
            updateStatus();
            mathAssessment.setupFallbackActivation({ type: 'drag-drop' });
            alert('Fallback test started. Wait 30 seconds to see the auto-enable feature.');
        }
        
        // Initial status update
        updateStatus();
        
        console.log('Interactive questions fix test initialized');
        console.log('Try interacting with the elements above to test the fixes');
    </script>
</body>
</html>
