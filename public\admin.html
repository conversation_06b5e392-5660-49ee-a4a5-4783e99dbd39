<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <title>Assessment Admin Dashboard</title>
</head>

<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-blue-900 text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold">Assessment Admin Dashboard</h1>
                <div class="flex items-center gap-4">
                    <select id="company-select" class="bg-blue-800 text-white border border-blue-700 rounded px-3 py-1">
                        <option value="Birmingham">Birmingham</option>
                    </select>
                    <button id="refresh-data" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors">
                        Refresh Data
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Assessment Type Tabs -->
        <div class="mb-8">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button id="english-tab" class="assessment-tab active py-2 px-1 border-b-2 font-medium text-sm">
                        English Assessments
                    </button>
                    <button id="math-tab" class="assessment-tab py-2 px-1 border-b-2 font-medium text-sm">
                        Mathematics Assessments
                    </button>
                    <button id="overview-tab" class="assessment-tab py-2 px-1 border-b-2 font-medium text-sm">
                        Overview
                    </button>
                </nav>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading" class="text-center py-8 hidden">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p class="mt-2 text-gray-600">Loading assessment data...</p>
        </div>

        <!-- English Assessment Panel -->
        <div id="english-panel" class="assessment-panel">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- English Stats Cards -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-semibold">📝</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total English Assessments</p>
                            <p id="english-total" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 font-semibold">📊</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Average Score</p>
                            <p id="english-avg-score" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <span class="text-yellow-600 font-semibold">⏱️</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Avg Duration</p>
                            <p id="english-avg-duration" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span class="text-purple-600 font-semibold">🎯</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">L2/GCSE Level</p>
                            <p id="english-l2-count" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- English Level Distribution Chart -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h3 class="text-lg font-semibold mb-4">English Proficiency Level Distribution</h3>
                <canvas id="english-level-chart" width="400" height="200"></canvas>
            </div>

            <!-- English Assessment List -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold">Recent English Assessments</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="english-assessments-table" class="bg-white divide-y divide-gray-200">
                            <!-- English assessments will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Mathematics Assessment Panel -->
        <div id="math-panel" class="assessment-panel hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Math Stats Cards -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-semibold">🔢</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Math Assessments</p>
                            <p id="math-total" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 font-semibold">📈</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Average Score</p>
                            <p id="math-avg-score" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <span class="text-yellow-600 font-semibold">⏱️</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Avg Duration</p>
                            <p id="math-avg-duration" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span class="text-purple-600 font-semibold">🏆</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">GCSE Level</p>
                            <p id="math-gcse-count" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Math Level Distribution Chart -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h3 class="text-lg font-semibold mb-4">Mathematics Level Distribution</h3>
                <canvas id="math-level-chart" width="400" height="200"></canvas>
            </div>

            <!-- Math Assessment List -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold">Recent Mathematics Assessments</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="math-assessments-table" class="bg-white divide-y divide-gray-200">
                            <!-- Math assessments will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Overview Panel -->
        <div id="overview-panel" class="assessment-panel hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Combined Statistics -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Assessment Overview</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Total English Assessments:</span>
                            <span id="overview-english-total" class="font-semibold">-</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Total Mathematics Assessments:</span>
                            <span id="overview-math-total" class="font-semibold">-</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Total Users Assessed:</span>
                            <span id="overview-total-users" class="font-semibold">-</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Average English Score:</span>
                            <span id="overview-english-avg" class="font-semibold">-</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Average Math Score:</span>
                            <span id="overview-math-avg" class="font-semibold">-</span>
                        </div>
                    </div>
                </div>

                <!-- Assessment Comparison Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Assessment Comparison</h3>
                    <canvas id="overview-comparison-chart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </main>

    <!-- Assessment Detail Modal -->
    <div id="assessment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 id="modal-title" class="text-lg font-semibold">Assessment Details</h3>
                        <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                            <span class="sr-only">Close</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div id="modal-content" class="px-6 py-4">
                    <!-- Assessment details will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script src="adminDashboard.js"></script>
</body>
</html>
