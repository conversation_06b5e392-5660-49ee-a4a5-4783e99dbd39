// ============================================================================
// STANDALONE MATHEMATICS ASSESSMENT SERVER
// ============================================================================
// This is a self-contained server focused specifically on mathematics assessment
// functionality, extracted from the main server for easier debugging and testing.

// Import required modules
require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const OpenAI = require('openai');
const admin = require('firebase-admin');
const cors = require('cors');
const NodeCache = require('node-cache');

// Initialize Express app
const app = express();
const port = process.env.PORT || 3003; // Use different port to avoid conflicts

// Configure middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Import Firebase service account key
const serviceAccount = require('./service_account.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/"
});

// Initialize Firebase services
const firestore = admin.firestore();

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Configured' : 'Missing');

// ============================================================================
// MATHEMATICS ASSESSMENT CONFIGURATION & CONSTANTS
// ============================================================================

// Mathematics Question Cache System
const mathQuestionCache = new Map();
const CACHE_EXPIRY_TIME = 60 * 60 * 1000; // 1 hour
const CACHE_MAX_SIZE = 200;
const API_TIMEOUT_THRESHOLD = 35000; // 35 seconds
const MAX_RETRY_ATTEMPTS = 1;
const PRELOAD_CACHE_ON_STARTUP = true;
const CONCURRENT_PRELOAD_LIMIT = 2;

// Performance metrics tracking
const performanceMetrics = {
  totalRequests: 0,
  cacheHits: 0,
  cacheMisses: 0,
  fallbackUsage: 0,
  averageGenerationTime: 0,
  apiTimeouts: 0,
  retryAttempts: 0,
  successfulRetries: 0
};

// Function to reset performance metrics to ensure proper numeric values
function resetPerformanceMetrics() {
  performanceMetrics.totalRequests = 0;
  performanceMetrics.cacheHits = 0;
  performanceMetrics.cacheMisses = 0;
  performanceMetrics.fallbackUsage = 0;
  performanceMetrics.averageGenerationTime = 0;
  performanceMetrics.apiTimeouts = 0;
  performanceMetrics.retryAttempts = 0;
  performanceMetrics.successfulRetries = 0;
}

// Get question specifications for each level
function getMathQuestionSpecs(level) {
  const specs = {
    'Entry': {
      count: 22,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 24,
      maxScore: 44,
      topics: ['arithmetic', 'fractions', 'percentages', 'basicAlgebra', 'measurement', 'dataHandling']
    },
    'Level1': {
      count: 13,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 16,
      maxScore: 26,
      topics: ['advancedArithmetic', 'fractionsDecimals', 'percentagesRatio', 'algebraicExpressions', 'geometry', 'statistics']
    },
    'GCSEPart1': {
      count: 7,
      timeLimit: 15 * 60, // 15 minutes
      passingScore: 5,
      maxScore: 10,
      topics: ['numberOperations', 'algebraicManipulation', 'geometricReasoning', 'fractionalCalculations']
    },
    'GCSEPart2': {
      count: 10,
      timeLimit: 20 * 60, // 20 minutes
      passingScore: 8,
      maxScore: 20,
      topics: ['complexCalculations', 'statisticalAnalysis', 'trigonometry', 'advancedAlgebra', 'problemSolving']
    }
  };

  return specs[level] || specs['Entry'];
}

// Get time limit for assessment level
function getMathTimeLimit(level) {
  return getMathQuestionSpecs(level).timeLimit;
}

// Get passing score for assessment level
function getMathPassingScore(level) {
  return getMathQuestionSpecs(level).passingScore;
}

// Get maximum score for assessment level
function getMathMaxScore(level) {
  return getMathQuestionSpecs(level).maxScore;
}

// ============================================================================
// DIGITAL SKILLS ASSESSMENT CONFIGURATION
// ============================================================================

// Digital Skills Question Cache System
const digitalSkillsQuestionCache = new Map();

// Get question specifications for each digital skills level
function getDigitalSkillsQuestionSpecs(level) {
  const specs = {
    'EntryLevel2': {
      count: 15,
      timeLimit: 25 * 60, // 25 minutes
      passingScore: 12,
      maxScore: 30,
      topics: ['computerBasics', 'mouseKeyboard', 'basicOperations', 'fileManagement', 'basicSafety']
    },
    'EntryLevel2Plus': {
      count: 18,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 15,
      maxScore: 36,
      topics: ['laptopDesktop', 'applications', 'internetSafety', 'emailBasics', 'digitalCitizenship']
    },
    'Level1': {
      count: 20,
      timeLimit: 35 * 60, // 35 minutes
      passingScore: 18,
      maxScore: 40,
      topics: ['microsoftApps', 'onlineBanking', 'cloudStorage', 'digitalIdentity', 'internetSkills']
    },
    'Level2': {
      count: 22,
      timeLimit: 40 * 60, // 40 minutes
      passingScore: 20,
      maxScore: 44,
      topics: ['advancedFormatting', 'spreadsheets', 'presentations', 'workplaceSkills', 'collaboration']
    },
    'EntryLevel3': {
      count: 16,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 14,
      maxScore: 32,
      topics: ['operatingSystems', 'emailProficiency', 'onlineTransactions', 'digitalSafety', 'troubleshooting']
    },
    'ICDLLevel2': {
      count: 25,
      timeLimit: 45 * 60, // 45 minutes
      passingScore: 22,
      maxScore: 50,
      topics: ['timedExam', 'wordAdvanced', 'excelAdvanced', 'powerpointAdvanced', 'employmentSkills']
    },
    'ICDLLevel3': {
      count: 30,
      timeLimit: 50 * 60, // 50 minutes
      passingScore: 26,
      maxScore: 60,
      topics: ['advancedMicrosoft', 'itCareers', 'higherEducation', 'professionalSkills', 'certification']
    }
  };

  return specs[level] || specs['EntryLevel2'];
}

// Get time limit for digital skills assessment level
function getDigitalSkillsTimeLimit(level) {
  return getDigitalSkillsQuestionSpecs(level).timeLimit;
}

// Get passing score for digital skills assessment level
function getDigitalSkillsPassingScore(level) {
  return getDigitalSkillsQuestionSpecs(level).passingScore;
}

// Get maximum score for digital skills assessment level
function getDigitalSkillsMaxScore(level) {
  return getDigitalSkillsQuestionSpecs(level).maxScore;
}

// ============================================================================
// CACHE MANAGEMENT FUNCTIONS
// ============================================================================

function getCacheKey(level, studentLevel) {
  return `${level}_${studentLevel || 'default'}`;
}

function getCachedQuestions(level, studentLevel) {
  const key = getCacheKey(level, studentLevel);
  const cached = mathQuestionCache.get(key);

  if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY_TIME) {
    performanceMetrics.cacheHits++;
    console.log(`Cache hit for ${key} - questions served from cache`);
    return cached.questions;
  }

  if (cached) {
    mathQuestionCache.delete(key); // Remove expired cache
    console.log(`Cache expired for ${key} - removing from cache`);
  }

  performanceMetrics.cacheMisses++;
  return null;
}

function setCachedQuestions(level, studentLevel, questions) {
  const key = getCacheKey(level, studentLevel);

  // Implement LRU cache by removing oldest entries if cache is full
  if (mathQuestionCache.size >= CACHE_MAX_SIZE) {
    const oldestKey = mathQuestionCache.keys().next().value;
    mathQuestionCache.delete(oldestKey);
    console.log(`Cache full - removed oldest entry: ${oldestKey}`);
  }

  mathQuestionCache.set(key, {
    questions: questions,
    timestamp: Date.now()
  });

  console.log(`Cached questions for ${key} - cache size: ${mathQuestionCache.size}`);
}

// Digital Skills Cache Management Functions
function getDigitalSkillsCacheKey(level, studentLevel) {
  return `digital_${level}_${studentLevel || 'default'}`;
}

function getCachedDigitalSkillsQuestions(level, studentLevel) {
  const key = getDigitalSkillsCacheKey(level, studentLevel);
  const cached = digitalSkillsQuestionCache.get(key);

  if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY_TIME) {
    performanceMetrics.cacheHits++;
    console.log(`Digital Skills cache hit for ${key} - questions served from cache`);
    return cached.questions;
  }

  if (cached) {
    digitalSkillsQuestionCache.delete(key); // Remove expired cache
    console.log(`Digital Skills cache expired for ${key} - removing from cache`);
  }

  performanceMetrics.cacheMisses++;
  return null;
}

function setCachedDigitalSkillsQuestions(level, studentLevel, questions) {
  const key = getDigitalSkillsCacheKey(level, studentLevel);

  // Implement LRU cache by removing oldest entries if cache is full
  if (digitalSkillsQuestionCache.size >= CACHE_MAX_SIZE) {
    const oldestKey = digitalSkillsQuestionCache.keys().next().value;
    digitalSkillsQuestionCache.delete(oldestKey);
    console.log(`Digital Skills cache full - removed oldest entry: ${oldestKey}`);
  }

  digitalSkillsQuestionCache.set(key, {
    questions: questions,
    timestamp: Date.now()
  });

  console.log(`Cached digital skills questions for ${key} - cache size: ${digitalSkillsQuestionCache.size}`);
}

function updateAverageGenerationTime(generationTime) {
  // Ensure generationTime is a number
  const numericGenerationTime = typeof generationTime === 'number' ? generationTime : 0;

  if (performanceMetrics.totalRequests === 1) {
    performanceMetrics.averageGenerationTime = numericGenerationTime;
  } else {
    // Ensure averageGenerationTime is a number
    const currentAverage = typeof performanceMetrics.averageGenerationTime === 'number'
      ? performanceMetrics.averageGenerationTime
      : 0;

    performanceMetrics.averageGenerationTime =
      (currentAverage * (performanceMetrics.totalRequests - 1) + numericGenerationTime) /
      performanceMetrics.totalRequests;
  }
}

function getPerformanceMetrics() {
  // Ensure all metrics are numbers
  const safeMetrics = {
    totalRequests: Number(performanceMetrics.totalRequests) || 0,
    cacheHits: Number(performanceMetrics.cacheHits) || 0,
    cacheMisses: Number(performanceMetrics.cacheMisses) || 0,
    fallbackUsage: Number(performanceMetrics.fallbackUsage) || 0,
    averageGenerationTime: Number(performanceMetrics.averageGenerationTime) || 0,
    apiTimeouts: Number(performanceMetrics.apiTimeouts) || 0,
    retryAttempts: Number(performanceMetrics.retryAttempts) || 0,
    successfulRetries: Number(performanceMetrics.successfulRetries) || 0
  };

  const cacheHitRate = safeMetrics.totalRequests > 0
    ? (safeMetrics.cacheHits / safeMetrics.totalRequests * 100).toFixed(2)
    : 0;

  const retrySuccessRate = safeMetrics.retryAttempts > 0
    ? (safeMetrics.successfulRetries / safeMetrics.retryAttempts * 100).toFixed(2)
    : 0;

  return {
    ...safeMetrics,
    cacheHitRate: `${cacheHitRate}%`,
    retrySuccessRate: `${retrySuccessRate}%`,
    cacheSize: mathQuestionCache.size
  };
}

// ============================================================================
// AI QUESTION GENERATION FUNCTIONS
// ============================================================================

// Enhanced AI prompt for high-quality mathematics question generation
function createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel) {
  const levelConfig = {
    'Entry': {
      description: 'Entry Level Mathematics - Foundation skills for adult learners',
      topics: {
        arithmetic: { weight: 30, description: 'Basic operations (+, -, ×, ÷) with whole numbers and simple decimals' },
        fractions: { weight: 20, description: 'Simple fractions (halves, quarters, thirds), basic operations' },
        percentages: { weight: 15, description: 'Common percentages (10%, 25%, 50%), percentage of amounts' },
        measurement: { weight: 15, description: 'Length, weight, capacity, time - basic conversions' },
        basicAlgebra: { weight: 10, description: 'Simple number patterns, basic substitution' },
        dataHandling: { weight: 10, description: 'Reading simple charts, basic statistics (mean, mode)' }
      }
    },
    'Level1': {
      description: 'Level 1 Mathematics - Building on foundation skills',
      topics: {
        advancedArithmetic: { weight: 25, description: 'Complex calculations, order of operations, negative numbers' },
        fractionsDecimals: { weight: 20, description: 'Converting between fractions/decimals, operations' },
        percentagesRatio: { weight: 20, description: 'Percentage increase/decrease, ratios, proportions' },
        algebraicExpressions: { weight: 15, description: 'Simple equations, substitution, rearranging formulae' },
        geometry: { weight: 10, description: 'Area, perimeter, basic shapes, angles' },
        statistics: { weight: 10, description: 'Interpreting data, probability basics' }
      }
    },
    'GCSEPart1': {
      description: 'GCSE Part 1 - Non-calculator mathematics',
      topics: {
        numberOperations: { weight: 30, description: 'Mental arithmetic, fractions, percentages without calculator' },
        algebraicManipulation: { weight: 30, description: 'Expanding brackets, factoring, solving equations' },
        geometricReasoning: { weight: 25, description: 'Angle properties, similarity, basic proofs' },
        fractionalCalculations: { weight: 15, description: 'Complex fraction operations, mixed numbers' }
      }
    },
    'GCSEPart2': {
      description: 'GCSE Part 2 - Calculator mathematics',
      topics: {
        complexCalculations: { weight: 25, description: 'Advanced calculations using calculator' },
        statisticalAnalysis: { weight: 25, description: 'Data analysis, correlation, probability distributions' },
        trigonometry: { weight: 25, description: 'Sin, cos, tan, solving triangles' },
        advancedAlgebra: { weight: 15, description: 'Quadratic equations, simultaneous equations' },
        problemSolving: { weight: 10, description: 'Multi-step real-world problems' }
      }
    }
  };

  const config = levelConfig[level] || levelConfig['Entry'];
  const topics = Object.keys(config.topics).map(topic => 
    `${topic}: ${config.topics[topic].description} (${config.topics[topic].weight}% weight)`
  ).join('\n');

  return `Generate ${questionSpecs.count} high-quality mathematics questions for ${config.description}.

LEVEL: ${level}
STUDENT TYPE: ${studentLevel || 'adult-learner'}
QUESTION COUNT: ${questionSpecs.count}
MAX SCORE: ${questionSpecs.maxScore}

TOPIC DISTRIBUTION:
${topics}

REQUIREMENTS:
- Each question must have: id, type, topic, question, options (for multiple-choice), correctAnswer, points, explanation
- Use UK mathematical terminology (maths, brackets, anticlockwise)
- Questions should be practical and relevant to adult learners
- Include variety: multiple-choice, numeric-input, short-answer
- Ensure progressive difficulty within the level
- Points should total approximately ${questionSpecs.maxScore}
- Make questions clear and unambiguous

QUESTION TYPES:
- multiple-choice: 4 options, one correct answer
- numeric: Numerical answer required
- short-answer: Brief mathematical expression

EXAMPLE FORMAT:
{
  "id": 1,
  "type": "multiple-choice",
  "topic": "arithmetic",
  "question": "What is 25 + 37?",
  "options": ["52", "62", "72", "82"],
  "correctAnswer": "62",
  "points": 2,
  "explanation": "25 + 37 = 62"
}

Return ONLY a valid JSON array of ${questionSpecs.count} questions.`;
}

// Helper function to make OpenAI API calls with retry logic
async function makeOpenAICallWithRetry(prompt, level, attempt = 1) {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('API timeout')), API_TIMEOUT_THRESHOLD);
  });

  const apiPromise = openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content: `You are an expert mathematics assessment designer specializing in UK educational standards. Your role is to create high-quality, educationally valuable mathematics questions for adult learners.

CRITICAL REQUIREMENTS:
- Return ONLY valid JSON array format
- No additional text, explanations, or markdown
- Each question must have all required fields
- Use UK mathematical terminology throughout
- Ensure questions are appropriate for the specified level`
      },
      {
        role: "user",
        content: prompt
      }
    ],
    max_tokens: 4000,
    temperature: 0.7
  });

  try {
    const completion = await Promise.race([apiPromise, timeoutPromise]);
    return completion;
  } catch (error) {
    if (error.message === 'API timeout') {
      performanceMetrics.apiTimeouts++;
      console.warn(`API timeout on attempt ${attempt} for ${level}`);
    }

    if (attempt < MAX_RETRY_ATTEMPTS) {
      performanceMetrics.retryAttempts++;
      console.log(`Retrying API call for ${level} (attempt ${attempt + 1}/${MAX_RETRY_ATTEMPTS})`);
      return makeOpenAICallWithRetry(prompt, level, attempt + 1);
    }

    throw error;
  }
}

// Parse AI response with multiple strategies
function parseAIQuestionsResponse(content, level) {
  console.log(`Parsing AI response for ${level}...`);

  // Strategy 1: Direct JSON parse
  try {
    const questions = JSON.parse(content);
    if (Array.isArray(questions) && questions.length > 0) {
      console.log(`✅ Direct JSON parse successful - ${questions.length} questions`);
      return questions;
    }
  } catch (error) {
    console.log('Direct JSON parse failed, trying extraction...');
  }

  // Strategy 2: Extract JSON from markdown or mixed content
  const jsonMatch = content.match(/\[[\s\S]*\]/);
  if (jsonMatch) {
    try {
      const questions = JSON.parse(jsonMatch[0]);
      if (Array.isArray(questions) && questions.length > 0) {
        console.log(`✅ JSON extraction successful - ${questions.length} questions`);
        return questions;
      }
    } catch (error) {
      console.log('JSON extraction failed');
    }
  }

  // Strategy 3: Clean and retry
  const cleanedContent = content
    .replace(/```json/g, '')
    .replace(/```/g, '')
    .replace(/^\s*[\w\s]*:\s*/, '')
    .trim();

  try {
    const questions = JSON.parse(cleanedContent);
    if (Array.isArray(questions) && questions.length > 0) {
      console.log(`✅ Cleaned JSON parse successful - ${questions.length} questions`);
      return questions;
    }
  } catch (error) {
    console.log('Cleaned JSON parse failed');
  }

  console.error('All parsing strategies failed');
  return null;
}

// Validate and normalize questions
function validateQuestions(questions, level) {
  const questionSpecs = getMathQuestionSpecs(level);
  const validQuestions = [];

  for (const question of questions) {
    // Validate required fields
    if (!question.id || !question.type || !question.topic || !question.question || !question.correctAnswer) {
      console.warn('Skipping invalid question - missing required fields:', question);
      continue;
    }

    // Normalize question structure
    const normalizedQuestion = {
      id: parseInt(question.id) || validQuestions.length + 1,
      type: question.type,
      topic: question.topic,
      question: question.question,
      correctAnswer: String(question.correctAnswer),
      points: parseInt(question.points) || 2,
      explanation: question.explanation || 'No explanation provided'
    };

    // Add options for multiple-choice questions
    if (question.type === 'multiple-choice' && question.options) {
      normalizedQuestion.options = question.options.map(opt => String(opt));
    }

    validQuestions.push(normalizedQuestion);
  }

  console.log(`Validated ${validQuestions.length}/${questions.length} questions for ${level}`);
  return validQuestions;
}

// Generate mathematics questions using AI with caching and performance optimization
async function generateMathematicsQuestions(level, studentLevel) {
  const startTime = Date.now();
  performanceMetrics.totalRequests++;

  try {
    console.log(`Generating mathematics questions for ${level} level...`);

    // Check cache first
    const cachedQuestions = getCachedQuestions(level, studentLevel);
    if (cachedQuestions) {
      const generationTime = Date.now() - startTime;
      console.log(`Questions served from cache in ${generationTime}ms`);
      updateAverageGenerationTime(generationTime);
      return cachedQuestions;
    }

    console.log(`🔄 Cache miss - generating new questions with AI (timeout: ${API_TIMEOUT_THRESHOLD}ms)...`);
    const questionSpecs = getMathQuestionSpecs(level);
    console.log(`📋 Question specs: ${questionSpecs.count} questions, max score: ${questionSpecs.maxScore}`);
    const prompt = createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel);

    // Make API call with retry logic
    let completion;
    try {
      completion = await makeOpenAICallWithRetry(prompt, level);
      const generationTime = Date.now() - startTime;
      console.log(`✅ OpenAI API call successful in ${generationTime}ms`);

      // Track successful retry if this wasn't the first attempt
      if (performanceMetrics.retryAttempts > 0) {
        performanceMetrics.successfulRetries++;
      }
    } catch (apiError) {
      console.error(`API call failed for ${level}:`, apiError.message);
      throw apiError;
    }

    // Parse and validate the response
    const content = completion.choices[0].message.content;
    console.log(`📝 Received ${content.length} characters from OpenAI`);

    const parsedQuestions = parseAIQuestionsResponse(content, level);
    if (!parsedQuestions) {
      throw new Error('Failed to parse AI response');
    }

    const questions = validateQuestions(parsedQuestions, level);
    if (questions.length === 0) {
      throw new Error('No valid questions generated');
    }

    // Ensure we have the right number of questions
    if (questions.length < questionSpecs.count) {
      console.warn(`Generated ${questions.length} questions, expected ${questionSpecs.count}. Using fallback for remaining.`);
      const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
      const additionalQuestions = fallbackQuestions.slice(questions.length);
      questions.push(...additionalQuestions.slice(0, questionSpecs.count - questions.length));
    }

    // Cache the questions
    setCachedQuestions(level, studentLevel, questions);

    const generationTime = Date.now() - startTime;
    console.log(`Generated ${questions.length} mathematics questions for ${level} level in ${generationTime}ms`);
    updateAverageGenerationTime(generationTime);

    return questions;

  } catch (error) {
    console.error('Error generating mathematics questions:', error);
    performanceMetrics.fallbackUsage++;
    const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
    const generationTime = Date.now() - startTime;
    updateAverageGenerationTime(generationTime);
    return fallbackQuestions;
  }
}

// ============================================================================
// FALLBACK QUESTION GENERATION
// ============================================================================

// Enhanced fallback question generation with better quality and coverage
function generateEnhancedFallbackMathQuestions(level) {
  // Check if we have cached fallback questions
  const fallbackCacheKey = `fallback_${level}`;
  const cachedFallback = mathQuestionCache.get(fallbackCacheKey);

  if (cachedFallback && Date.now() - cachedFallback.timestamp < CACHE_EXPIRY_TIME) {
    console.log(`Using cached fallback questions for ${level}`);
    return cachedFallback.questions;
  }

  // Get question specifications for the level
  const questionSpecs = getMathQuestionSpecs(level);

  console.log(`🔧 Generating enhanced fallback questions for ${level} level...`);

  // Use the improved fallback generation with deduplication
  const result = generateFallbackMathQuestions(level);

  // Cache the fallback questions for future use
  mathQuestionCache.set(fallbackCacheKey, {
    questions: result,
    timestamp: Date.now()
  });

  console.log(`✅ Generated and cached ${result.length} enhanced fallback questions for ${level}`);
  return result;
}

// Keep original fallback function for backward compatibility
function generateFallbackMathQuestions(level) {
  const fallbackQuestions = {
    'Entry': [
      // Arithmetic Questions (30% - 7 questions)
      {
        id: 1,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "What is 25 + 37?",
        options: ["52", "62", "72", "82"],
        correctAnswer: "62",
        points: 2,
        explanation: "25 + 37 = 62"
      },
      {
        id: 2,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "What is 84 - 29?",
        options: ["45", "55", "65", "75"],
        correctAnswer: "55",
        points: 2,
        explanation: "84 - 29 = 55"
      },
      {
        id: 3,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "What is 8 × 7?",
        options: ["54", "56", "58", "64"],
        correctAnswer: "56",
        points: 2,
        explanation: "8 × 7 = 56"
      },
      {
        id: 4,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "What is 144 ÷ 12?",
        options: ["11", "12", "13", "14"],
        correctAnswer: "12",
        points: 2,
        explanation: "144 ÷ 12 = 12"
      },
      {
        id: 5,
        type: "numeric",
        topic: "arithmetic",
        question: "Calculate 15 + 28 - 9",
        correctAnswer: "34",
        points: 2,
        explanation: "15 + 28 - 9 = 43 - 9 = 34"
      },
      {
        id: 6,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "What is 6 × 9?",
        options: ["52", "54", "56", "58"],
        correctAnswer: "54",
        points: 2,
        explanation: "6 × 9 = 54"
      },
      {
        id: 7,
        type: "numeric",
        topic: "arithmetic",
        question: "What is 72 ÷ 8?",
        correctAnswer: "9",
        points: 2,
        explanation: "72 ÷ 8 = 9"
      },
      // Fractions Questions (20% - 4 questions)
      {
        id: 8,
        type: "multiple-choice",
        topic: "fractions",
        question: "What is 1/2 + 1/4?",
        options: ["1/6", "2/6", "3/4", "1/3"],
        correctAnswer: "3/4",
        points: 2,
        explanation: "1/2 + 1/4 = 2/4 + 1/4 = 3/4"
      },
      {
        id: 9,
        type: "multiple-choice",
        topic: "fractions",
        question: "What is 3/4 of 20?",
        options: ["12", "15", "16", "18"],
        correctAnswer: "15",
        points: 2,
        explanation: "3/4 × 20 = (3 × 20) ÷ 4 = 60 ÷ 4 = 15"
      },
      {
        id: 10,
        type: "multiple-choice",
        topic: "fractions",
        question: "Which fraction is equivalent to 0.5?",
        options: ["1/3", "1/2", "2/3", "3/4"],
        correctAnswer: "1/2",
        points: 2,
        explanation: "0.5 = 5/10 = 1/2"
      },
      {
        id: 11,
        type: "multiple-choice",
        topic: "fractions",
        question: "What is 2/3 - 1/6?",
        options: ["1/2", "1/3", "1/6", "2/9"],
        correctAnswer: "1/2",
        points: 2,
        explanation: "2/3 - 1/6 = 4/6 - 1/6 = 3/6 = 1/2"
      },
      // Percentages Questions (15% - 3 questions)
      {
        id: 12,
        type: "multiple-choice",
        topic: "percentages",
        question: "What is 25% of 80?",
        options: ["15", "20", "25", "30"],
        correctAnswer: "20",
        points: 2,
        explanation: "25% of 80 = 25/100 × 80 = 0.25 × 80 = 20"
      },
      {
        id: 13,
        type: "multiple-choice",
        topic: "percentages",
        question: "What percentage is 15 out of 60?",
        options: ["20%", "25%", "30%", "35%"],
        correctAnswer: "25%",
        points: 2,
        explanation: "15/60 = 1/4 = 0.25 = 25%"
      },
      {
        id: 14,
        type: "numeric",
        topic: "percentages",
        question: "A shirt costs £40. If there's a 10% discount, what is the sale price?",
        correctAnswer: "36",
        points: 2,
        explanation: "10% of £40 = £4, so sale price = £40 - £4 = £36"
      }
    ],
    'Level1': [
      // Advanced Arithmetic Questions (25% - 3 questions)
      {
        id: 1,
        type: "multiple-choice",
        topic: "advancedArithmetic",
        question: "What is (-5) + 8 - (-3)?",
        options: ["6", "10", "16", "0"],
        correctAnswer: "6",
        points: 2,
        explanation: "(-5) + 8 - (-3) = -5 + 8 + 3 = 6"
      },
      {
        id: 2,
        type: "multiple-choice",
        topic: "advancedArithmetic",
        question: "Calculate 2 + 3 × 4 - 1",
        options: ["13", "19", "21", "23"],
        correctAnswer: "13",
        points: 2,
        explanation: "Following order of operations: 2 + (3 × 4) - 1 = 2 + 12 - 1 = 13"
      },
      {
        id: 3,
        type: "numeric",
        topic: "advancedArithmetic",
        question: "What is 15² - 200?",
        correctAnswer: "25",
        points: 2,
        explanation: "15² = 225, so 225 - 200 = 25"
      }
    ]
  };

  const questions = fallbackQuestions[level] || fallbackQuestions['Entry'];
  const questionSpecs = getMathQuestionSpecs(level);

  // Ensure we have enough questions by repeating if necessary
  const result = [];
  for (let i = 0; i < questionSpecs.count; i++) {
    const questionIndex = i % questions.length;
    const baseQuestion = questions[questionIndex];
    result.push({
      ...baseQuestion,
      id: i + 1 // Ensure unique IDs
    });
  }

  return result.slice(0, questionSpecs.count);
}

// ============================================================================
// DIGITAL SKILLS ASSESSMENT FUNCTIONS
// ============================================================================

// Generate digital skills questions using AI with fallback
async function generateDigitalSkillsQuestions(level, studentLevel) {
  const startTime = Date.now();
  performanceMetrics.totalRequests++;

  try {
    console.log(`Generating digital skills questions for ${level} level...`);

    // Check cache first
    const cachedQuestions = getCachedDigitalSkillsQuestions(level, studentLevel);
    if (cachedQuestions) {
      const generationTime = Date.now() - startTime;
      console.log(`Digital skills questions served from cache in ${generationTime}ms`);
      updateAverageGenerationTime(generationTime);
      return cachedQuestions;
    }

    console.log(`🔄 Cache miss - generating new digital skills questions with AI (timeout: ${API_TIMEOUT_THRESHOLD}ms)...`);
    const questionSpecs = getDigitalSkillsQuestionSpecs(level);
    console.log(`📋 Digital skills question specs: ${questionSpecs.count} questions, max score: ${questionSpecs.maxScore}`);

    // Try to generate with AI first, but fall back to template questions if it fails
    let questions;
    try {
      const prompt = createDigitalSkillsQuestionPrompt(level, questionSpecs, studentLevel);
      const completion = await makeDigitalSkillsOpenAICall(prompt, level);
      const content = completion.choices[0].message.content;
      console.log(`📝 Received ${content.length} characters from OpenAI for digital skills`);

      const parsedQuestions = parseAIQuestionsResponse(content, level);
      if (parsedQuestions && parsedQuestions.length > 0) {
        questions = validateDigitalSkillsQuestions(parsedQuestions, level);
        console.log(`✅ Generated ${questions.length} digital skills questions with AI`);
      } else {
        throw new Error('Failed to parse AI response');
      }
    } catch (error) {
      console.log(`⚠️ AI generation failed, using fallback questions: ${error.message}`);
      questions = generateFallbackDigitalSkillsQuestions(level);
    }

    // Ensure we have the right number of questions
    if (questions.length < questionSpecs.count) {
      console.warn(`Generated ${questions.length} digital skills questions, expected ${questionSpecs.count}. Using fallback for remaining.`);
      const fallbackQuestions = generateFallbackDigitalSkillsQuestions(level);
      const additionalQuestions = fallbackQuestions.slice(questions.length);
      questions.push(...additionalQuestions.slice(0, questionSpecs.count - questions.length));
    }

    // Cache the questions
    setCachedDigitalSkillsQuestions(level, studentLevel, questions);

    const generationTime = Date.now() - startTime;
    console.log(`Generated ${questions.length} digital skills questions for ${level} level in ${generationTime}ms`);
    updateAverageGenerationTime(generationTime);

    return questions;

  } catch (error) {
    console.error('Error generating digital skills questions:', error);
    performanceMetrics.fallbackUsage++;
    const fallbackQuestions = generateFallbackDigitalSkillsQuestions(level);
    const generationTime = Date.now() - startTime;
    updateAverageGenerationTime(generationTime);
    return fallbackQuestions;
  }
}

// Helper function to make OpenAI API calls for digital skills assessment
async function makeDigitalSkillsOpenAICall(prompt, level, attempt = 1) {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('API timeout')), API_TIMEOUT_THRESHOLD);
  });

  const apiPromise = openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content: `You are an expert digital skills assessment designer specializing in computer literacy and digital competency evaluation. Your role is to create high-quality, practical digital skills questions for adult learners and students.

CRITICAL REQUIREMENTS:
- Return ONLY valid JSON array format
- No additional text, explanations, or markdown
- Each question must have all required fields
- Questions should be practical and relevant to real-world digital skills
- Focus on computer literacy, internet skills, and digital applications`
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 4000
  });

  try {
    const completion = await Promise.race([apiPromise, timeoutPromise]);
    return completion;
  } catch (error) {
    console.error(`Digital skills OpenAI API call failed (attempt ${attempt}):`, error.message);

    if (error.message === 'API timeout') {
      performanceMetrics.timeouts++;
    }

    if (attempt < MAX_RETRY_ATTEMPTS) {
      performanceMetrics.retryAttempts++;
      console.log(`Retrying digital skills API call for ${level} (attempt ${attempt + 1}/${MAX_RETRY_ATTEMPTS})`);
      return makeDigitalSkillsOpenAICall(prompt, level, attempt + 1);
    }

    throw error;
  }
}

// Create optimized prompt for digital skills question generation
function createDigitalSkillsQuestionPrompt(level, questionSpecs, studentLevel) {
  const topicDescriptions = {
    'computerBasics': 'Basic computer parts, terminology, and concepts',
    'mouseKeyboard': 'Mouse and keyboard usage, navigation, and shortcuts',
    'basicOperations': 'Turning on/off devices, basic file operations, simple tasks',
    'fileManagement': 'Creating, saving, and organizing files and folders',
    'basicSafety': 'Basic internet safety, password creation, and privacy',
    'laptopDesktop': 'Understanding differences between laptops and desktops',
    'applications': 'Using basic applications and software',
    'internetSafety': 'Online safety, security, and responsible internet use',
    'emailBasics': 'Setting up and using email, attachments, and etiquette',
    'digitalCitizenship': 'Digital identity, online behavior, and ethics',
    'microsoftApps': 'Using Microsoft Word, Excel, and PowerPoint basics',
    'onlineBanking': 'Safe online banking and financial transactions',
    'cloudStorage': 'Using cloud storage services like Google Drive or OneDrive',
    'digitalIdentity': 'Managing online profiles and digital footprint',
    'internetSkills': 'Advanced internet searching, evaluating sources',
    'advancedFormatting': 'Advanced document formatting and layout',
    'spreadsheets': 'Creating and using spreadsheets for calculations',
    'presentations': 'Designing effective presentations',
    'workplaceSkills': 'Digital skills for workplace environments',
    'collaboration': 'Online collaboration tools and practices',
    'operatingSystems': 'Understanding operating systems and their features',
    'emailProficiency': 'Advanced email management and organization',
    'onlineTransactions': 'Secure online shopping and transactions',
    'digitalSafety': 'Advanced digital safety and security practices',
    'troubleshooting': 'Basic troubleshooting for common computer issues',
    'timedExam': 'Timed exam format preparation',
    'wordAdvanced': 'Advanced Microsoft Word features',
    'excelAdvanced': 'Advanced Microsoft Excel features',
    'powerpointAdvanced': 'Advanced Microsoft PowerPoint features',
    'employmentSkills': 'Digital skills for employment',
    'advancedMicrosoft': 'Advanced Microsoft application features',
    'itCareers': 'Preparation for IT careers',
    'higherEducation': 'Digital skills for higher education',
    'professionalSkills': 'Professional digital skills',
    'certification': 'Preparation for certification exams'
  };

  // Create topic distribution based on level
  const topicDistribution = questionSpecs.topics.map(topic => {
    return {
      topic,
      description: topicDescriptions[topic] || topic,
      count: Math.max(1, Math.floor(questionSpecs.count / questionSpecs.topics.length))
    };
  });

  // Adjust for student level
  const difficultyAdjustment = studentLevel === 'adult-learner' ?
    'Ensure questions are appropriate for adult learners with limited prior computer experience.' :
    'Adapt questions to be suitable for students with some basic digital literacy.';

  return `
  You are an expert digital skills assessment creator. Create ${questionSpecs.count} digital skills assessment questions for ${level} level.

  QUESTION DISTRIBUTION:
  ${topicDistribution.map(t => `- ${t.count} questions on ${t.topic}: ${t.description}`).join('\n')}

  QUESTION FORMAT REQUIREMENTS:
  1. Each question must be multiple-choice with 4 options (A, B, C, D)
  2. Each question must have exactly one correct answer
  3. Each question must be worth 2 points
  4. Include a mix of theoretical knowledge and practical application questions
  5. Questions should assess both conceptual understanding and practical skills
  6. ${difficultyAdjustment}

  RESPONSE FORMAT:
  Return a valid JSON array of question objects with this exact structure:
  [
    {
      "id": 1,
      "question": "Question text here",
      "options": ["Option A", "Option B", "Option C", "Option D"],
      "correctAnswer": "Option A",
      "points": 2,
      "topic": "topicName",
      "type": "multiple-choice"
    },
    ...
  ]

  IMPORTANT:
  - Ensure all JSON is valid with no syntax errors
  - Each question must have a unique ID starting from 1
  - Do not include any explanations or text outside the JSON array
  - Questions should be clear, concise, and unambiguous
  - Avoid overly technical jargon unless appropriate for the level
  - Include practical, real-world scenarios relevant to the level
  `;
}

// Validate digital skills questions
function validateDigitalSkillsQuestions(questions, level) {
  const questionSpecs = getDigitalSkillsQuestionSpecs(level);
  const validQuestions = [];

  for (const question of questions) {
    // Basic validation
    if (!question.question || !question.options || !question.correctAnswer) {
      console.warn('Skipping invalid question:', question);
      continue;
    }

    // Ensure options is an array with 4 items
    if (!Array.isArray(question.options) || question.options.length !== 4) {
      console.warn('Question has invalid options:', question);
      continue;
    }

    // Ensure correctAnswer is one of the options
    if (!question.options.includes(question.correctAnswer)) {
      console.warn('Question has invalid correctAnswer:', question);
      continue;
    }

    // Normalize question structure
    validQuestions.push({
      id: question.id || validQuestions.length + 1,
      question: question.question,
      options: question.options,
      correctAnswer: question.correctAnswer,
      points: question.points || 2,
      topic: question.topic || questionSpecs.topics[0],
      type: question.type || 'multiple-choice'
    });
  }

  console.log(`Validated ${validQuestions.length}/${questions.length} digital skills questions for ${level}`);
  return validQuestions;
}

// Generate fallback digital skills questions if AI generation fails
function generateFallbackDigitalSkillsQuestions(level) {
  console.log(`Generating fallback digital skills questions for ${level} level`);

  const questionSpecs = getDigitalSkillsQuestionSpecs(level);
  const result = [];

  // Basic template questions for each level
  const templates = {
    'EntryLevel2': [
      {
        question: "Which of these is a pointing device used to control the cursor on a computer screen?",
        options: ["Mouse", "Keyboard", "Monitor", "Printer"],
        correctAnswer: "Mouse",
        topic: "mouseKeyboard"
      },
      {
        question: "What is the main function of a computer's CPU?",
        options: ["To process data", "To store data permanently", "To connect to the internet", "To display images"],
        correctAnswer: "To process data",
        topic: "computerBasics"
      }
    ],
    'EntryLevel2Plus': [
      {
        question: "Which of these is a good practice for creating a strong password?",
        options: [
          "Using a combination of letters, numbers, and symbols",
          "Using your name and birth date",
          "Using the same password for all accounts",
          "Writing it down on a sticky note on your monitor"
        ],
        correctAnswer: "Using a combination of letters, numbers, and symbols",
        topic: "internetSafety"
      }
    ],
    'Level1': [
      {
        question: "In Microsoft Word, which feature would you use to check spelling and grammar?",
        options: [
          "Spelling & Grammar check",
          "Format Painter",
          "Track Changes",
          "Mail Merge"
        ],
        correctAnswer: "Spelling & Grammar check",
        topic: "microsoftApps"
      }
    ]
  };

  // Use templates for the specific level or default to EntryLevel2
  const levelTemplates = templates[level] || templates['EntryLevel2'];

  // Generate the required number of questions
  for (let i = 0; i < questionSpecs.count; i++) {
    // Cycle through templates if we need more questions than templates
    const templateIndex = i % levelTemplates.length;
    const template = levelTemplates[templateIndex];

    result.push({
      id: i + 1,
      question: template.question,
      options: template.options,
      correctAnswer: template.correctAnswer,
      points: 2,
      topic: template.topic,
      type: 'multiple-choice'
    });
  }

  return result.slice(0, questionSpecs.count);
}

// ============================================================================
// ASSESSMENT ANALYSIS FUNCTIONS
// ============================================================================

// Create AI prompt for assessment analysis
function createMathAnalysisPrompt(answers, level, timeSpent, questions) {
  const questionSpecs = getMathQuestionSpecs(level);

  // Create a map of question IDs to questions for easy lookup
  const questionMap = {};
  if (questions && Array.isArray(questions)) {
    questions.forEach(q => {
      questionMap[q.id] = q;
    });
  }

  return `Analyze this mathematics assessment for ${level} level:

ASSESSMENT DETAILS:
- Level: ${level}
- Questions: ${questionSpecs.count}
- Maximum Score: ${questionSpecs.maxScore}
- Passing Score: ${questionSpecs.passingScore}
- Time Spent: ${timeSpent || 'unknown'} seconds
- Answers Provided: ${answers.length}

STUDENT RESPONSES:
${answers.map((answer, index) => {
  const question = questionMap[answer.questionId];
  const questionText = question ? question.question : 'Question not found';
  const correctAnswer = question ? question.correctAnswer : 'Unknown';
  const points = question ? question.points : 2;

  return `Question ${index + 1}: "${questionText}" - Student Answer: "${answer.studentAnswer || 'No answer'}" - Correct Answer: "${correctAnswer}" - Topic: ${answer.topic || 'unknown'} - Points: ${points}`;
}).join('\n')}

SCORING INSTRUCTIONS:
1. Compare each student answer with the correct answer
2. Award full points for exact matches (case-insensitive for text answers)
3. Award partial credit for close numerical answers (within reasonable rounding)
4. Sum up all points earned to get the total score
5. Determine pass/fail based on whether score >= passing score (${questionSpecs.passingScore})

Please provide a comprehensive analysis in JSON format with:
{
  "score": <total_points_earned_as_number>,
  "passed": <true_or_false>,
  "topicBreakdown": {
    "arithmetic": {"correct": 0, "total": 0, "percentage": 0},
    "fractions": {"correct": 0, "total": 0, "percentage": 0},
    "percentages": {"correct": 0, "total": 0, "percentage": 0},
    "algebra": {"correct": 0, "total": 0, "percentage": 0},
    "geometry": {"correct": 0, "total": 0, "percentage": 0},
    "statistics": {"correct": 0, "total": 0, "percentage": 0}
  },
  "feedback": {
    "numericalSkills": "<detailed_feedback>",
    "algebraicThinking": "<detailed_feedback>",
    "problemSolving": "<detailed_feedback>",
    "geometricReasoning": "<detailed_feedback>",
    "dataHandling": "<detailed_feedback>",
    "overall": "<overall_assessment>"
  },
  "strengths": ["<strength_1>", "<strength_2>"],
  "improvements": ["<improvement_1>", "<improvement_2>"],
  "placementRecommendation": {
    "level": "<recommended_level>",
    "reasoning": "<explanation>",
    "nextSteps": ["<step_1>", "<step_2>"],
    "courseRecommendations": ["<course_1>", "<course_2>"]
  }
}

CRITICAL REQUIREMENTS:
- Return ONLY valid JSON, no additional text or markdown
- Score must be a number, not a string
- Ensure all feedback uses UK mathematical terminology (e.g., 'maths' not 'math', 'brackets' not 'parentheses', 'anticlockwise' not 'counterclockwise')
- Base recommendations on actual demonstrated competency, not just completion
- Calculate score by carefully comparing student answers to correct answers`;
}

// Generate fallback analysis when AI analysis fails
function generateFallbackMathAnalysis(answers, level, questions) {
  const questionSpecs = getMathQuestionSpecs(level);

  console.log('DEBUG - Generating fallback analysis with:', {
    answerCount: answers.length,
    level,
    hasQuestions: !!questions,
    questionCount: questions ? questions.length : 0
  });

  // Create a map of question IDs to questions for easy lookup
  const questionMap = {};
  if (questions && Array.isArray(questions)) {
    questions.forEach(q => {
      questionMap[q.id] = q;
    });
  }

  // Calculate score based on correct answers
  let score = 0;
  let correctCount = 0;

  answers.forEach((answer, index) => {
    const question = questionMap[answer.questionId];
    if (question && answer.studentAnswer) {
      const studentAns = String(answer.studentAnswer).trim().toLowerCase();
      const correctAns = String(question.correctAnswer).trim().toLowerCase();
      const points = question.points || 2;

      console.log(`DEBUG - Question ${index + 1}: Student="${studentAns}" vs Correct="${correctAns}" Points=${points}`);

      if (studentAns === correctAns) {
        score += points;
        correctCount++;
        console.log(`DEBUG - Correct! Added ${points} points. Total: ${score}`);
      }
    } else {
      console.log(`DEBUG - Question ${index + 1}: Missing question data or student answer`);
    }
  });

  const passed = score >= questionSpecs.passingScore;

  console.log('DEBUG - Fallback analysis result:', {
    score,
    correctCount,
    totalQuestions: answers.length,
    passingScore: questionSpecs.passingScore,
    passed
  });

  return {
    score: score,
    passed: passed,
    topicBreakdown: {},
    feedback: {
      numericalSkills: `Answered ${correctCount} out of ${answers.length} questions correctly`,
      algebraicThinking: 'Algebraic skills evaluated',
      problemSolving: 'Problem-solving assessed',
      geometricReasoning: 'Geometric understanding reviewed',
      dataHandling: 'Data handling skills assessed',
      overall: `Mathematics assessment completed with ${score} points out of ${questionSpecs.maxScore}`
    },
    strengths: correctCount > 0 ? [`Correctly answered ${correctCount} questions`] : ['Completed the mathematics assessment'],
    improvements: score < questionSpecs.passingScore ? ['Continue practicing mathematics to improve score'] : ['Continue building on current skills'],
    placementRecommendation: {
      level: passed ? level : 'Entry Support',
      reasoning: `Based on score of ${score}/${questionSpecs.maxScore} points`,
      nextSteps: passed ? ['Continue to next level'] : ['Review fundamental concepts'],
      courseRecommendations: passed ? ['Advanced mathematics course'] : ['Basic mathematics course']
    }
  };
}

// Analyze mathematics assessment using AI
async function analyzeMathematicsAssessment(answers, level, timeSpent, questions) {
  try {
    console.log('Analyzing mathematics assessment with AI...');
    console.log('DEBUG - Answers received for analysis:', {
      answerCount: answers.length,
      sampleAnswers: answers.slice(0, 3),
      level,
      timeSpent,
      questionsProvided: !!questions,
      questionCount: questions ? questions.length : 0
    });

    const analysisPrompt = createMathAnalysisPrompt(answers, level, timeSpent, questions);
    console.log('DEBUG - Analysis prompt created, length:', analysisPrompt.length);

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an expert mathematics assessor specialising in evaluating mathematical proficiency for UK educational placement. Provide detailed analysis using UK mathematical terminology and educational standards."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.3
    });

    const content = completion.choices[0].message.content;
    console.log('AI analysis response received');
    console.log('DEBUG - AI response content:', content.substring(0, 500) + '...');

    let analysisResult;
    try {
      // Try direct JSON parse first
      analysisResult = JSON.parse(content);
      console.log('DEBUG - Direct JSON parse successful');
    } catch (parseError) {
      console.log('DEBUG - Direct JSON parse failed, trying extraction...');

      // Try to extract JSON from markdown or mixed content
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          analysisResult = JSON.parse(jsonMatch[0]);
          console.log('DEBUG - JSON extraction successful');
        } catch (extractError) {
          console.log('DEBUG - JSON extraction failed, trying cleanup...');

          // Try cleaning the content
          const cleanedContent = content
            .replace(/```json/g, '')
            .replace(/```/g, '')
            .replace(/^\s*[\w\s]*:\s*/, '')
            .trim();

          try {
            analysisResult = JSON.parse(cleanedContent);
            console.log('DEBUG - Cleaned JSON parse successful');
          } catch (cleanError) {
            console.warn('All JSON parsing strategies failed, using fallback');
            console.log('DEBUG - Parse error:', parseError.message);
            console.log('DEBUG - Raw content that failed to parse:', content.substring(0, 1000));
            return generateFallbackMathAnalysis(answers, level, questions);
          }
        }
      } else {
        console.warn('No JSON found in response, using fallback');
        console.log('DEBUG - Raw content:', content.substring(0, 1000));
        return generateFallbackMathAnalysis(answers, level, questions);
      }
    }

    console.log('DEBUG - Parsed analysis result:', {
      score: analysisResult.score,
      scoreType: typeof analysisResult.score,
      passed: analysisResult.passed,
      hasTopicBreakdown: !!analysisResult.topicBreakdown,
      hasFeedback: !!analysisResult.feedback
    });

    // Validate and normalize the results
    const maxScore = getMathMaxScore(level);
    const passingScore = getMathPassingScore(level);

    analysisResult.score = Math.max(0, Math.min(maxScore, parseInt(analysisResult.score) || 0));
    analysisResult.passed = analysisResult.score >= passingScore;
    analysisResult.level = level;
    analysisResult.maxScore = maxScore;
    analysisResult.passingScore = passingScore;

    console.log('Mathematics assessment analysis completed:', {
      level,
      score: analysisResult.score,
      maxScore,
      passed: analysisResult.passed
    });

    return analysisResult;

  } catch (error) {
    console.error('Error in AI mathematics assessment analysis:', error);
    return generateFallbackMathAnalysis(answers, level, questions);
  }
}

// ============================================================================
// DATABASE STORAGE FUNCTIONS
// ============================================================================

// Store mathematics assessment results in database with comprehensive response logging
async function storeMathematicsAssessmentResults(email, level, analysisResult, answers, timeSpent, detailedResponses = null, userData = null) {
  try {
    const userCompany = 'Birmingham'; // Default company for student users

    // Prepare level-specific data
    const levelData = {
      completed: true,
      score: analysisResult.score,
      passed: analysisResult.passed,
      timeSpent: timeSpent || 0,
      completedAt: new Date(),
      responses: answers,
      topicBreakdown: analysisResult.topicBreakdown || {}
    };

    // Prepare update data with basic user information for document creation (matching main server structure)
    const updateData = {
      // Basic user information (in case document doesn't exist)
      userEmail: email,
      userCompany: userCompany,

      // Mathematics assessment data (matching main server structure)
      mathAssessmentCompleted: true,
      mathCurrentLevel: level,
      mathOverallScore: analysisResult.score,
      mathAssessmentTimestamp: new Date(),
      totalTimeSpentOnMath: timeSpent || 0,
      updatedAt: new Date(),

      // Store feedback in flattened structure
      mathFeedback: analysisResult.feedback || {},
      mathStrengths: analysisResult.strengths || [],
      mathImprovements: analysisResult.improvements || [],
      mathPlacementRecommendation: analysisResult.placementRecommendation || {}
    };

    // Add level-specific data (matching main server structure)
    switch (level) {
      case 'Entry':
        updateData.mathEntryLevel = levelData;
        updateData.mathHighestLevelCompleted = 'Entry';
        break;
      case 'Level1':
        updateData.mathLevel1 = levelData;
        updateData.mathHighestLevelCompleted = 'Level1';
        break;
      case 'GCSEPart1':
        updateData.mathGCSEPart1 = levelData;
        updateData.mathHighestLevelCompleted = 'GCSEPart1';
        break;
      case 'GCSEPart2':
        updateData.mathGCSEPart2 = levelData;
        updateData.mathHighestLevelCompleted = 'GCSEPart2';
        break;
    }

    // Add required dashboard fields if userData is provided
    if (userData) {
      updateData.firstName = userData.firstName;
      updateData.lastName = userData.lastName;
      updateData.name = userData.name || `${userData.firstName} ${userData.lastName}`;
      updateData.userType = userData.userType || 'student';
      updateData.studentLevel = userData.studentLevel;
      updateData.userEmail = email; // Required for dashboard
      updateData.userCompany = userCompany; // Required for dashboard
      updateData.status = 'completed'; // Set status as completed
      updateData.createdAt = updateData.createdAt || new Date(); // Set creation time if not exists

      console.log('Adding dashboard fields for user:', {
        name: updateData.name,
        userType: updateData.userType,
        studentLevel: updateData.studentLevel
      });
    }

    // Add detailed responses if provided (using flattened structure)
    if (detailedResponses) {
      updateData.mathDetailedResponses = {
        questionResponses: detailedResponses.questionResponses || [],
        interactionLog: detailedResponses.interactionLog || [],
        metadata: {
          userAgent: detailedResponses.userAgent || 'unknown',
          screenResolution: detailedResponses.screenResolution || 'unknown',
          timestamp: new Date(),
          responseCount: detailedResponses.questionResponses?.length || 0,
          interactionCount: detailedResponses.interactionLog?.length || 0
        }
      };

      console.log('Storing detailed responses:', {
        questionResponsesCount: detailedResponses.questionResponses?.length || 0,
        interactionLogCount: detailedResponses.interactionLog?.length || 0
      });
    }

    // Ensure company document exists (especially for Birmingham)
    const companyRef = firestore.collection('companies').doc(userCompany);
    const companyDoc = await companyRef.get();

    if (!companyDoc.exists && userCompany === 'Birmingham') {
      console.log('Creating Birmingham company document for mathematics assessment');
      await companyRef.set({
        name: 'Birmingham',
        type: 'student-focused',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        description: 'Auto-created company for student assessments'
      });
    }

    // Create or update user document (using set with merge to handle non-existent documents)
    const userRef = companyRef.collection('users').doc(email);

    console.log('Storing mathematics assessment data for user:', {
      email,
      company: userCompany,
      level,
      operation: 'set with merge'
    });

    // Use set with merge option to create document if it doesn't exist
    await userRef.set(updateData, { merge: true });

    console.log('Mathematics assessment results stored successfully:', {
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed,
      documentCreated: true,
      company: userCompany
    });

  } catch (error) {
    console.error('Error storing mathematics assessment results:', error);
    throw error;
  }
}

// Initialize Birmingham company for student-focused version
async function initializeBirminghamCompany() {
  try {
    console.log('Checking Birmingham company setup...');
    const companyDoc = await firestore
      .collection('companies')
      .doc('Birmingham')
      .get();

    if (!companyDoc.exists) {
      console.log('Creating Birmingham company for student users...');
      await firestore
        .collection('companies')
        .doc('Birmingham')
        .set({
          name: 'Birmingham',
          type: 'student-focused',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          description: 'Auto-created company for student assessments'
        });
      console.log('Birmingham company created successfully');
    } else {
      console.log('Birmingham company already exists');
    }
  } catch (error) {
    console.error('Error initializing Birmingham company:', error);
  }
}

// ============================================================================
// MATHEMATICS ASSESSMENT API ENDPOINTS
// ============================================================================

// Performance monitoring endpoint
app.get('/api/math-assessments/performance', (req, res) => {
  try {
    const metrics = getPerformanceMetrics();

    res.status(200).json({
      success: true,
      metrics: {
        ...metrics,
        cacheEntries: Array.from(mathQuestionCache.keys()),
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    res.status(500).json({
      error: 'Failed to get performance metrics',
      details: error.message
    });
  }
});

// Cache management endpoints
app.post('/api/math-assessments/cache/clear', (req, res) => {
  try {
    const cacheSize = mathQuestionCache.size;
    mathQuestionCache.clear();

    // Reset performance metrics
    resetPerformanceMetrics();

    res.status(200).json({
      success: true,
      message: `Cleared ${cacheSize} cache entries and reset performance metrics`
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({
      error: 'Failed to clear cache',
      details: error.message
    });
  }
});

// Cache warming endpoint
app.post('/api/math-assessments/cache/warm', async (req, res) => {
  try {
    const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
    const studentLevels = ['adult-learner', 'returning-student'];
    const results = [];

    console.log('Starting cache warming...');

    for (const level of levels) {
      for (const studentLevel of studentLevels) {
        try {
          console.log(`Warming cache for ${level} - ${studentLevel}`);
          const questions = await generateMathematicsQuestions(level, studentLevel);
          results.push({
            level,
            studentLevel,
            questionCount: questions.length,
            status: 'success'
          });
        } catch (error) {
          console.error(`Failed to warm cache for ${level} - ${studentLevel}:`, error);
          results.push({
            level,
            studentLevel,
            status: 'failed',
            error: error.message
          });
        }
      }
    }

    res.status(200).json({
      success: true,
      message: 'Cache warming completed',
      results,
      cacheSize: mathQuestionCache.size
    });
  } catch (error) {
    console.error('Error warming cache:', error);
    res.status(500).json({
      error: 'Failed to warm cache',
      details: error.message
    });
  }
});

// Start Mathematics Assessment - Generate questions for specific level
app.post('/api/math-assessments/start', async (req, res) => {
  console.log('Received mathematics assessment start request');

  try {
    const { level, email, studentLevel } = req.body;

    // Validate required fields
    if (!level || !email) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          level: !level ? 'Missing assessment level' : null,
          email: !email ? 'Missing email' : null
        }
      });
    }

    // Validate level
    const validLevels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
    if (!validLevels.includes(level)) {
      return res.status(400).json({
        error: 'Invalid assessment level',
        details: `Level must be one of: ${validLevels.join(', ')}`
      });
    }

    console.log('Generating mathematics questions for:', {
      email,
      level,
      studentLevel
    });

    // Generate questions using AI
    const questions = await generateMathematicsQuestions(level, studentLevel);

    // Create assessment session
    const assessmentId = `math_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log('Mathematics assessment started:', {
      assessmentId,
      email,
      level,
      questionCount: questions.length
    });

    res.status(200).json({
      assessmentId,
      level,
      questions,
      timeLimit: getMathTimeLimit(level),
      passingScore: getMathPassingScore(level),
      maxScore: getMathMaxScore(level)
    });

  } catch (error) {
    console.error('Error starting mathematics assessment:', error);
    res.status(500).json({
      error: 'Failed to start mathematics assessment',
      details: error.message
    });
  }
});

// Submit Mathematics Assessment - Process answers and provide results
app.post('/api/math-assessments/:id/submit', async (req, res) => {
  console.log('Received mathematics assessment submission');

  try {
    const { id: assessmentId } = req.params;
    const { answers, email, level, timeSpent, detailedResponses, userData } = req.body;

    // Validate required fields
    if (!answers || !email || !level) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          answers: !answers ? 'Missing answers' : null,
          email: !email ? 'Missing email' : null,
          level: !level ? 'Missing level' : null
        }
      });
    }

    console.log('Processing mathematics assessment submission:', {
      assessmentId,
      email,
      level,
      answerCount: answers.length,
      timeSpent: timeSpent || 'unknown',
      hasDetailedResponses: !!detailedResponses,
      detailedResponsesCount: detailedResponses?.questionResponses?.length || 0,
      interactionLogCount: detailedResponses?.interactionLog?.length || 0,
      hasUserData: !!userData,
      userDataFields: userData ? Object.keys(userData) : []
    });

    // Debug: Log the first few answers to understand the structure
    console.log('DEBUG - Sample answers structure:', {
      firstAnswer: answers[0],
      secondAnswer: answers[1],
      totalAnswers: answers.length
    });

    // Get the questions for this level to provide context for analysis
    const questions = await generateMathematicsQuestions(level, 'adult-learner');

    // Analyze answers with AI
    const analysisResult = await analyzeMathematicsAssessment(answers, level, timeSpent, questions);

    // Store results in database with comprehensive response logging
    await storeMathematicsAssessmentResults(email, level, analysisResult, answers, timeSpent, detailedResponses, userData);

    console.log('Mathematics assessment completed:', {
      assessmentId,
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed
    });

    res.status(200).json(analysisResult);

  } catch (error) {
    console.error('Error submitting mathematics assessment:', error);
    res.status(500).json({
      error: 'Failed to submit mathematics assessment',
      details: error.message
    });
  }
});

// Get mathematics assessment analytics and statistics
app.get('/api/admin/math-analytics', async (req, res) => {
  try {
    const { company = 'Birmingham' } = req.query;

    const snapshot = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('mathAssessmentCompleted', '==', true)
      .get();

    const analytics = {
      totalAssessments: 0,
      levelBreakdown: {
        Entry: { completed: 0, passed: 0, averageScore: 0 },
        Level1: { completed: 0, passed: 0, averageScore: 0 },
        GCSEPart1: { completed: 0, passed: 0, averageScore: 0 },
        GCSEPart2: { completed: 0, passed: 0, averageScore: 0 }
      },
      overallPassRate: 0,
      averageTimeSpent: 0
    };

    let totalTimeSpent = 0;
    let totalPassed = 0;

    snapshot.forEach(doc => {
      const data = doc.data();
      if (data.mathAssessmentCompleted) {
        analytics.totalAssessments++;

        // Check each level using the flattened structure
        const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
        const levelFields = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];

        levels.forEach((level, index) => {
          const levelData = data[levelFields[index]];
          if (levelData && levelData.completed) {
            analytics.levelBreakdown[level].completed++;
            if (levelData.passed) {
              analytics.levelBreakdown[level].passed++;
              totalPassed++;
            }
            analytics.levelBreakdown[level].averageScore += levelData.score || 0;
            totalTimeSpent += levelData.timeSpent || 0;
          }
        });
      }
    });

    // Calculate averages
    Object.keys(analytics.levelBreakdown).forEach(level => {
      const levelStats = analytics.levelBreakdown[level];
      if (levelStats.completed > 0) {
        levelStats.averageScore = Math.round(levelStats.averageScore / levelStats.completed);
        levelStats.passRate = Math.round((levelStats.passed / levelStats.completed) * 100);
      }
    });

    analytics.overallPassRate = analytics.totalAssessments > 0
      ? Math.round((totalPassed / analytics.totalAssessments) * 100)
      : 0;

    analytics.averageTimeSpent = analytics.totalAssessments > 0
      ? Math.round(totalTimeSpent / analytics.totalAssessments)
      : 0;

    res.status(200).json({
      success: true,
      analytics,
      company
    });

  } catch (error) {
    console.error('Error getting mathematics analytics:', error);
    res.status(500).json({
      error: 'Failed to get mathematics analytics',
      details: error.message
    });
  }
});

// ============================================================================
// DIGITAL SKILLS ASSESSMENT API ENDPOINTS
// ============================================================================

// Start Digital Skills Assessment - Generate questions for the specified level
app.post('/api/digital-skills-assessments/start', async (req, res) => {
  console.log('Received digital skills assessment start request');

  try {
    const { level, email, studentLevel } = req.body;

    // Validate required fields
    if (!level || !email) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          level: !level ? 'Missing level' : null,
          email: !email ? 'Missing email' : null
        }
      });
    }

    console.log('Starting digital skills assessment:', {
      level,
      email,
      studentLevel
    });

    // Generate questions using AI
    const questions = await generateDigitalSkillsQuestions(level, studentLevel);

    // Create assessment session
    const assessmentId = `digital_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log('Digital skills assessment started:', {
      assessmentId,
      email,
      level,
      questionCount: questions.length
    });

    res.status(200).json({
      assessmentId,
      level,
      questions,
      timeLimit: getDigitalSkillsTimeLimit(level),
      passingScore: getDigitalSkillsPassingScore(level),
      maxScore: getDigitalSkillsMaxScore(level)
    });

  } catch (error) {
    console.error('Error starting digital skills assessment:', error);
    res.status(500).json({
      error: 'Failed to start digital skills assessment',
      details: error.message
    });
  }
});

// Submit Digital Skills Assessment - Process answers and provide results
app.post('/api/digital-skills-assessments/:id/submit', async (req, res) => {
  console.log('Received digital skills assessment submission');

  try {
    const { id: assessmentId } = req.params;
    const { answers, email, level, timeSpent, detailedResponses, userData } = req.body;

    // Validate required fields
    if (!answers || !email || !level) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          answers: !answers ? 'Missing answers' : null,
          email: !email ? 'Missing email' : null,
          level: !level ? 'Missing level' : null
        }
      });
    }

    console.log('Processing digital skills assessment submission:', {
      assessmentId,
      email,
      level,
      answerCount: answers.length,
      timeSpent
    });

    // Simple scoring for now (can be enhanced with AI analysis later)
    const questionSpecs = getDigitalSkillsQuestionSpecs(level);
    let score = 0;
    const topicBreakdown = {};

    // Calculate score and topic breakdown
    answers.forEach(answer => {
      if (answer.answer && answer.answer.trim() !== '') {
        score += 2; // Each question worth 2 points for now
      }
    });

    const passed = score >= questionSpecs.passingScore;

    const analysisResult = {
      score,
      passed,
      maxScore: questionSpecs.maxScore,
      passingScore: questionSpecs.passingScore,
      level,
      timeSpent: timeSpent || 0,
      topicBreakdown,
      strengths: passed ? ['Good overall performance'] : ['Completed the assessment'],
      improvements: passed ? ['Continue building skills'] : ['Review fundamental concepts'],
      feedback: `You scored ${score} out of ${questionSpecs.maxScore} points on the ${level} digital skills assessment.`,
      recommendations: ['Practice regularly', 'Focus on weaker areas'],
      nextSteps: passed ? ['Consider advancing to next level'] : ['Review and practice current level']
    };

    console.log('Digital skills assessment completed:', {
      assessmentId,
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed
    });

    res.status(200).json(analysisResult);

  } catch (error) {
    console.error('Error submitting digital skills assessment:', error);
    res.status(500).json({
      error: 'Failed to submit digital skills assessment',
      details: error.message
    });
  }
});

// Root endpoint - serve the mathematics assessment page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'math.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'Mathematics Assessment Server',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    cacheSize: mathQuestionCache.size,
    performanceMetrics: getPerformanceMetrics()
  });
});

// ============================================================================
// SERVER STARTUP
// ============================================================================

// Preload mathematics question cache for better performance
async function preloadMathQuestionCache() {
  if (!PRELOAD_CACHE_ON_STARTUP) {
    console.log('Cache preloading disabled');
    return;
  }

  console.log('🚀 Starting mathematics question cache preloading...');
  const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
  const studentLevels = ['adult-learner', 'returning-student'];

  let preloadPromises = [];
  let activePreloads = 0;

  for (const level of levels) {
    for (const studentLevel of studentLevels) {
      // Limit concurrent preloads
      if (activePreloads >= CONCURRENT_PRELOAD_LIMIT) {
        await Promise.race(preloadPromises);
        activePreloads--;
      }

      const preloadPromise = generateMathematicsQuestions(level, studentLevel)
        .then(() => {
          console.log(`✅ Preloaded cache for ${level} - ${studentLevel}`);
          activePreloads--;
        })
        .catch(error => {
          console.warn(`⚠️ Failed to preload ${level} - ${studentLevel}:`, error.message);
          activePreloads--;
        });

      preloadPromises.push(preloadPromise);
      activePreloads++;
    }
  }

  // Wait for all preloads to complete
  await Promise.allSettled(preloadPromises);
  console.log(`🎯 Cache preloading completed. Cache size: ${mathQuestionCache.size}`);
}

// Start the server
app.listen(port, async () => {
  console.log(`🧮 Mathematics Assessment Server is running on http://localhost:${port}`);
  console.log(`📊 Performance monitoring available at http://localhost:${port}/api/math-assessments/performance`);
  console.log(`🏥 Health check available at http://localhost:${port}/health`);

  // Debug: Check initial performance metrics
  console.log('Initial performance metrics:', JSON.stringify(getPerformanceMetrics(), null, 2));

  // Initialize Birmingham company for student-focused version
  await initializeBirminghamCompany();

  // Cache preloading is handled during startup, not here
  console.log('🎯 Mathematics Assessment Server ready for use!');
});
