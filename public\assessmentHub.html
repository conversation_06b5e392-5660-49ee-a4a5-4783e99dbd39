<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <title>Assessment Hub - Skills Gap Analyzer</title>
    <style>
        body {
            background: linear-gradient(135deg, #1547bb 0%, #121c41 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .hub-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .hub-header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .hub-title {
            color: #ffffff;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .hub-subtitle {
            color: #e8f0ff;
            font-size: 1.2rem;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .assessment-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }
        
        .assessment-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
        }
        
        .assessment-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1547bb 0%, #0d3a8a 100%);
        }
        
        .card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .card-title {
            color: #121c41;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 12px;
            line-height: 1.3;
        }
        
        .card-description {
            color: #6b7280;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .card-details {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #374151;
            font-size: 0.9rem;
        }
        
        .detail-icon {
            color: #1547bb;
            font-size: 1rem;
        }
        
        .card-action {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #1547bb;
            font-weight: 600;
            font-size: 1rem;
            margin-top: auto;
        }
        
        .card-action .arrow {
            transition: transform 0.3s ease;
        }
        
        .assessment-card:hover .card-action .arrow {
            transform: translateX(4px);
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-title {
            color: #ffffff;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .info-text {
            color: #e8f0ff;
            font-size: 1rem;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .hub-title {
                font-size: 2.2rem;
            }
            
            .hub-subtitle {
                font-size: 1.1rem;
            }
            
            .assessment-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .assessment-card {
                padding: 25px;
            }
            
            .card-title {
                font-size: 1.3rem;
            }
        }
        
        @media (max-width: 480px) {
            .hub-container {
                padding: 20px 15px;
            }
            
            .hub-title {
                font-size: 1.8rem;
            }
            
            .assessment-card {
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="hub-container">
        <div class="hub-header">
            <h1 class="hub-title">Assessment Hub</h1>
            <p class="hub-subtitle">Choose your assessment type to get started with personalized skills evaluation and recommendations</p>
        </div>

        <div class="assessment-grid">
            <!-- English Proficiency Assessment -->
            <a href="SGA.html" class="assessment-card">
                <span class="card-icon">📝</span>
                <h3 class="card-title">English Proficiency Assessment</h3>
                <p class="card-description">Evaluate your English language skills with interactive questions and writing tasks. Get detailed feedback on grammar, vocabulary, and communication abilities.</p>
                <div class="card-details">
                    <div class="detail-item">
                        <span class="detail-icon">⏱️</span>
                        <span>30 minutes</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">📊</span>
                        <span>Entry to GCSE Level</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">✍️</span>
                        <span>Writing & Grammar</span>
                    </div>
                </div>
                <div class="card-action">
                    <span>Start English Assessment</span>
                    <span class="arrow">→</span>
                </div>
            </a>

            <!-- Digital Skills Assessment -->
            <a href="digitalSkillsAssessment.html" class="assessment-card">
                <span class="card-icon">💻</span>
                <h3 class="card-title">Digital Skills Assessment</h3>
                <p class="card-description">Test your computer and digital literacy skills across 7 competency levels. From basic computer operations to advanced Microsoft applications and ICDL certification preparation.</p>
                <div class="card-details">
                    <div class="detail-item">
                        <span class="detail-icon">⏱️</span>
                        <span>25-50 minutes</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">📊</span>
                        <span>7 Skill Levels</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">🖥️</span>
                        <span>Computer Skills</span>
                    </div>
                </div>
                <div class="card-action">
                    <span>Start Digital Skills Assessment</span>
                    <span class="arrow">→</span>
                </div>
            </a>

            <!-- Mathematics Assessment -->
            <a href="math.html" class="assessment-card">
                <span class="card-icon">🔢</span>
                <h3 class="card-title">Mathematics Assessment</h3>
                <p class="card-description">Assess your mathematical abilities from basic arithmetic to GCSE level. Interactive questions with step-by-step problem solving and detailed performance analysis.</p>
                <div class="card-details">
                    <div class="detail-item">
                        <span class="detail-icon">⏱️</span>
                        <span>15-30 minutes</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">📊</span>
                        <span>Entry to GCSE</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">🧮</span>
                        <span>Interactive Math</span>
                    </div>
                </div>
                <div class="card-action">
                    <span>Start Mathematics Assessment</span>
                    <span class="arrow">→</span>
                </div>
            </a>
        </div>

        <div class="info-section">
            <h3 class="info-title">How It Works</h3>
            <p class="info-text">
                Each assessment is designed to evaluate your current skill level and provide personalized recommendations for improvement. 
                You can take assessments individually or complete multiple assessments for a comprehensive skills evaluation. 
                All results include detailed feedback and suggested learning pathways.
            </p>
        </div>
    </div>

    <script>
        // Add click tracking for analytics
        document.querySelectorAll('.assessment-card').forEach(card => {
            card.addEventListener('click', function(e) {
                const assessmentType = this.querySelector('.card-title').textContent;
                console.log(`Assessment selected: ${assessmentType}`);
                
                // You can add analytics tracking here
                // gtag('event', 'assessment_selected', {
                //     'assessment_type': assessmentType
                // });
            });
        });

        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                const focusedElement = document.activeElement;
                if (focusedElement.classList.contains('assessment-card')) {
                    e.preventDefault();
                    focusedElement.click();
                }
            }
        });
    </script>
</body>
</html>
