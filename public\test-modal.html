<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal - Mathematics Assessment</title>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <style>
        body {
            padding: 2rem;
            background: #f3f4f6;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            margin: 1rem 0;
            display: block;
            width: 100%;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            color: #0c4a6e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Mathematics Assessment Modal Test</h1>
        <p>This page tests the detailed report modal functionality.</p>
        
        <div class="test-info">
            <strong>Test Instructions:</strong>
            <ol>
                <li>Click "Setup Test Data" to create mock assessment results</li>
                <li>Click "Test Modal" to open the detailed report modal</li>
                <li>Verify all sections are populated correctly</li>
                <li>Test modal interactions (close, print, etc.)</li>
            </ol>
        </div>

        <button class="test-button" onclick="setupTestData()">Setup Test Data</button>
        <button class="test-button" onclick="testModal()">Test Modal</button>
        <button class="test-button" onclick="checkConsole()">Check Console Logs</button>
        
        <div id="test-results" style="margin-top: 2rem; padding: 1rem; background: #f9fafb; border-radius: 8px; display: none;">
            <h3>Test Results:</h3>
            <div id="test-output"></div>
        </div>
    </div>

    <!-- Include the modal HTML -->
    <div id="detailed-report-modal" class="modal-overlay hidden">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title">📊 Detailed Mathematics Assessment Report</h2>
                <button id="close-modal-btn" class="modal-close-btn" aria-label="Close modal">
                    <span class="close-icon">×</span>
                </button>
            </div>
            
            <div class="modal-content">
                <!-- Assessment Summary -->
                <div class="report-section">
                    <h3 class="section-title">📈 Assessment Summary</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">Level:</span>
                            <span id="modal-level" class="summary-value">Entry</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Score:</span>
                            <span id="modal-score" class="summary-value">20 / 44</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Percentage:</span>
                            <span id="modal-percentage" class="summary-value">45%</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Status:</span>
                            <span id="modal-status" class="summary-value">In Progress</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Time Taken:</span>
                            <span id="modal-time" class="summary-value">25 minutes</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Questions:</span>
                            <span id="modal-questions" class="summary-value">22</span>
                        </div>
                    </div>
                </div>

                <!-- Topic Breakdown -->
                <div class="report-section">
                    <h3 class="section-title">📚 Topic Performance Breakdown</h3>
                    <div id="modal-topic-breakdown" class="topic-breakdown-detailed">
                        <!-- Topic breakdown will be populated here -->
                    </div>
                </div>

                <!-- Feedback Section -->
                <div class="report-section">
                    <h3 class="section-title">💬 Detailed Feedback</h3>
                    <div id="modal-feedback" class="feedback-detailed">
                        <!-- Detailed feedback will be populated here -->
                    </div>
                </div>

                <!-- Strengths -->
                <div class="report-section">
                    <h3 class="section-title">💪 Your Strengths</h3>
                    <div id="modal-strengths" class="strengths-list">
                        <!-- Strengths will be populated here -->
                    </div>
                </div>

                <!-- Areas for Improvement -->
                <div class="report-section">
                    <h3 class="section-title">🎯 Areas for Improvement</h3>
                    <div id="modal-improvements" class="improvements-list">
                        <!-- Improvements will be populated here -->
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="report-section">
                    <h3 class="section-title">🚀 Recommended Next Steps</h3>
                    <div id="modal-recommendations" class="recommendations-detailed">
                        <!-- Recommendations will be populated here -->
                    </div>
                </div>

                <!-- Course Recommendations -->
                <div class="report-section">
                    <h3 class="section-title">📖 Suggested Courses</h3>
                    <div id="modal-courses" class="courses-list">
                        <!-- Course recommendations will be populated here -->
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button id="close-modal-footer-btn" class="modal-action-btn primary">
                    <span class="btn-text">Close</span>
                </button>
            </div>
        </div>
    </div>

    <script src="mathAssessment.js"></script>
    <script>
        let testAssessment = null;

        function setupTestData() {
            testAssessment = new MathAssessment();
            
            // Create mock assessment results
            testAssessment.assessmentResults = {
                score: 20,
                maxScore: 44,
                passed: false,
                topicBreakdown: {
                    arithmetic: 6,
                    fractions: 4,
                    percentages: 2,
                    basicAlgebra: 3,
                    measurement: 3,
                    dataHandling: 2
                },
                feedback: {
                    numericalSkills: "Good understanding of basic arithmetic operations.",
                    algebraicThinking: "Needs improvement in algebraic concepts and problem-solving.",
                    problemSolving: "Shows potential but requires more practice with word problems.",
                    overall: "Solid foundation with room for improvement in advanced topics."
                },
                strengths: [
                    "Strong performance in basic arithmetic calculations",
                    "Good understanding of measurement concepts",
                    "Consistent approach to problem-solving"
                ],
                improvements: [
                    "Practice more fraction operations and conversions",
                    "Work on percentage calculations and applications",
                    "Strengthen algebraic thinking and equation solving"
                ],
                placementRecommendation: {
                    level: "Entry Support",
                    reasoning: "Student shows good foundational skills but needs additional support in key areas before progressing.",
                    nextSteps: [
                        "Focus on fraction operations and decimal conversions",
                        "Practice percentage problems in real-world contexts",
                        "Build confidence with basic algebraic concepts"
                    ],
                    courseRecommendations: [
                        "Entry Level Functional Skills Mathematics",
                        "Basic Numeracy Workshop",
                        "Fractions and Decimals Masterclass"
                    ]
                }
            };

            testAssessment.assessmentLevel = 'Entry';
            testAssessment.timeSpent = 1500; // 25 minutes in seconds
            testAssessment.levelSpecs = {
                'Entry': { questionCount: 22 }
            };

            showTestResults('Test data setup complete! Mock assessment results created.');
        }

        function testModal() {
            if (!testAssessment) {
                showTestResults('Please setup test data first!');
                return;
            }

            console.log('Testing modal...');
            testAssessment.showDetailedReportModal();
            showTestResults('Modal test initiated. Check if modal opened correctly.');
        }

        function checkConsole() {
            showTestResults('Check the browser console (F12) for detailed logs and any error messages.');
        }

        function showTestResults(message) {
            const resultsDiv = document.getElementById('test-results');
            const outputDiv = document.getElementById('test-output');
            
            outputDiv.innerHTML = `<p><strong>${new Date().toLocaleTimeString()}:</strong> ${message}</p>` + outputDiv.innerHTML;
            resultsDiv.style.display = 'block';
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
        });
    </script>
</body>
</html>
