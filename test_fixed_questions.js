require('dotenv').config();
const OpenAI = require('openai');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');

// Copy the updated prompt function from server.js
function createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel) {
  const levelConfig = {
    'Entry': {
      topics: 'Basic arithmetic, simple fractions, basic percentages, introductory algebra, measurement, basic data handling',
      difficulty: 'Foundation level for adult learners',
      examples: [
        '{"id":1,"type":"multiple-choice","topic":"arithmetic","question":"What is 25 + 37?","options":["52","62","72","82"],"correctAnswer":"62","points":2}',
        '{"id":2,"type":"numeric","topic":"measurement","question":"How many millilitres are in 1 litre?","correctAnswer":"1000","points":2}',
        '{"id":3,"type":"short-answer","topic":"fractions","question":"Simplify 4/8","correctAnswer":"1/2","points":2}'
      ]
    },
    'Level1': {
      topics: 'Advanced arithmetic, fractions/decimals, percentages/ratio, algebraic expressions, geometry, statistics, measurement conversions',
      difficulty: 'Intermediate level building on foundation',
      examples: [
        '{"id":1,"type":"multiple-choice","topic":"measurement","question":"Convert 150cm to metres","options":["0.15m","1.5m","15m","150m"],"correctAnswer":"1.5m","points":2}',
        '{"id":2,"type":"numeric","topic":"fractions","question":"What is 3/4 + 1/8? (Enter as decimal)","correctAnswer":"0.875","points":2}',
        '{"id":3,"type":"short-answer","topic":"algebra","question":"If 3x + 5 = 20, what is x?","correctAnswer":"5","points":2}'
      ]
    }
  };

  const config = levelConfig[level] || levelConfig['Level1'];

  return `Generate exactly ${questionSpecs.count} mathematics questions for ${level} level.

CRITICAL REQUIREMENTS:
- Return ONLY a valid JSON array
- Use UK mathematical terminology and conventions
- Include measurement conversion questions (ml/litres, cm/metres, g/kg)
- Include fraction questions (addition, simplification, of/multiplication)

QUESTION TYPES (use exactly these names):
1. "multiple-choice": MUST have "options" array with exactly 4 choices
2. "numeric": For numerical answers (decimals, integers)
3. "short-answer": For algebraic expressions, simplified fractions

REQUIRED FIELDS for ALL questions:
- id: sequential number
- type: one of the three types above
- topic: subject area (arithmetic, fractions, measurement, algebra, etc.)
- question: clear question text
- correctAnswer: the correct answer as string
- points: integer score value

EXAMPLES:
${config.examples.join('\n')}

Generate exactly ${questionSpecs.count} questions with varied types and topics:`;
}

// Copy normalization function
function normalizeQuestionTypes(questions) {
  if (!Array.isArray(questions)) {
    return questions;
  }

  return questions.map(question => {
    const normalizedQuestion = { ...question };
    
    // Fix question type naming inconsistencies
    if (normalizedQuestion.type === 'multiple_choice') {
      normalizedQuestion.type = 'multiple-choice';
      console.log(`Normalized question ${question.id}: multiple_choice → multiple-choice`);
    } else if (normalizedQuestion.type === 'text') {
      normalizedQuestion.type = 'short-answer';
      console.log(`Normalized question ${question.id}: text → short-answer`);
    }
    
    // Ensure multiple-choice questions have proper options
    if (normalizedQuestion.type === 'multiple-choice') {
      if (!normalizedQuestion.options || !Array.isArray(normalizedQuestion.options)) {
        console.warn(`Question ${question.id}: multiple-choice missing options array`);
        return null; // Will be filtered out
      }
      
      // Ensure exactly 4 options
      if (normalizedQuestion.options.length < 4) {
        console.warn(`Question ${question.id}: multiple-choice has only ${normalizedQuestion.options.length} options`);
        return null; // Will be filtered out
      } else if (normalizedQuestion.options.length > 4) {
        normalizedQuestion.options = normalizedQuestion.options.slice(0, 4);
        console.log(`Question ${question.id}: trimmed options to 4`);
      }
    }
    
    // Ensure numeric and short-answer questions don't have options
    if ((normalizedQuestion.type === 'numeric' || normalizedQuestion.type === 'short-answer') && normalizedQuestion.options) {
      delete normalizedQuestion.options;
      console.log(`Question ${question.id}: removed options array from ${normalizedQuestion.type} question`);
    }
    
    return normalizedQuestion;
  }).filter(q => q !== null); // Remove invalid questions
}

// Test the complete fixed system
async function testFixedMathematicsSystem() {
  console.log('🧮 Testing Fixed Mathematics Assessment System\n');
  
  const questionSpecs = { count: 8 };
  const prompt = createOptimizedMathQuestionPrompt('Level1', questionSpecs, 'adult-learner');
  
  console.log('Generated prompt:');
  console.log('='.repeat(50));
  console.log(prompt);
  console.log('='.repeat(50));
  
  try {
    const startTime = Date.now();
    
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are a mathematics assessment expert. Generate questions in valid JSON format with correct question types and all required fields."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1200,
      temperature: 0.1
    });
    
    const duration = Date.now() - startTime;
    console.log(`\n✅ AI response received in ${duration}ms`);
    
    const responseText = completion.choices[0].message.content;
    console.log('Response length:', responseText.length);
    
    // Parse response
    let cleanedText = responseText;
    cleanedText = cleanedText.replace(/```json\s*/g, '').replace(/```\s*/g, '').replace(/`/g, '');
    
    const jsonMatch = cleanedText.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      let questions = JSON.parse(jsonMatch[0]);
      console.log(`\n📝 Parsed ${questions.length} questions`);
      
      // Apply normalization
      console.log('\n🔧 Applying normalization...');
      questions = normalizeQuestionTypes(questions);
      console.log(`📝 After normalization: ${questions.length} questions`);
      
      // Test each question
      console.log('\n📊 Question Analysis:');
      console.log('='.repeat(80));
      
      let measurementCount = 0;
      let fractionCount = 0;
      let multipleChoiceCount = 0;
      let numericCount = 0;
      let shortAnswerCount = 0;
      
      questions.forEach((q, index) => {
        console.log(`\nQuestion ${index + 1}:`);
        console.log(`- ID: ${q.id}`);
        console.log(`- Type: ${q.type}`);
        console.log(`- Topic: ${q.topic}`);
        console.log(`- Question: ${q.question}`);
        console.log(`- Correct Answer: ${q.correctAnswer}`);
        console.log(`- Points: ${q.points}`);
        
        if (q.type === 'multiple-choice') {
          console.log(`- Options: [${q.options.join(', ')}]`);
          multipleChoiceCount++;
        } else if (q.type === 'numeric') {
          numericCount++;
        } else if (q.type === 'short-answer') {
          shortAnswerCount++;
        }
        
        if (q.topic === 'measurement') measurementCount++;
        if (q.topic === 'fractions') fractionCount++;
        
        // Frontend compatibility check
        console.log('- Frontend compatibility:');
        if (q.type === 'multiple-choice') {
          if (q.options && Array.isArray(q.options) && q.options.length === 4) {
            console.log('  ✅ Will render multiple choice with 4 options');
          } else {
            console.log('  ❌ Multiple choice missing proper options');
          }
        } else if (q.type === 'numeric') {
          console.log('  ✅ Will render numeric input field');
        } else if (q.type === 'short-answer') {
          console.log('  ✅ Will render short answer input field');
        } else {
          console.log(`  ❌ Unknown type "${q.type}" - will default to short answer`);
        }
      });
      
      // Summary
      console.log('\n📈 Summary:');
      console.log('='.repeat(50));
      console.log(`Total questions: ${questions.length}`);
      console.log(`Question types:`);
      console.log(`  - Multiple choice: ${multipleChoiceCount}`);
      console.log(`  - Numeric: ${numericCount}`);
      console.log(`  - Short answer: ${shortAnswerCount}`);
      console.log(`Topic coverage:`);
      console.log(`  - Measurement questions: ${measurementCount}`);
      console.log(`  - Fraction questions: ${fractionCount}`);
      
      // Check if we have the required question types
      const hasVariety = multipleChoiceCount > 0 && (numericCount > 0 || shortAnswerCount > 0);
      const hasMeasurement = measurementCount > 0;
      const hasFractions = fractionCount > 0;
      
      console.log('\n🎯 Requirements Check:');
      console.log(`✅ Question type variety: ${hasVariety ? 'PASS' : 'FAIL'}`);
      console.log(`✅ Measurement questions: ${hasMeasurement ? 'PASS' : 'FAIL'}`);
      console.log(`✅ Fraction questions: ${hasFractions ? 'PASS' : 'FAIL'}`);
      
      if (hasVariety && hasMeasurement && hasFractions) {
        console.log('\n🎉 SUCCESS: All requirements met!');
      } else {
        console.log('\n⚠️  Some requirements not fully met, but system is functional');
      }
      
    } else {
      console.log('❌ Could not extract JSON from response');
      console.log('Raw response:', responseText);
    }
    
  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
  }
}

// Run the test
runTest().catch(console.error);

async function runTest() {
  console.log('🔧 Mathematics Assessment Question Type Fix Test\n');
  console.log('This test verifies that:');
  console.log('✓ AI generates questions with correct types (multiple-choice, numeric, short-answer)');
  console.log('✓ Multiple choice questions have exactly 4 options');
  console.log('✓ Measurement and fraction questions are included');
  console.log('✓ Questions are compatible with frontend rendering');
  console.log('✓ Question type normalization works correctly\n');
  
  await testFixedMathematicsSystem();
  
  console.log('\n🎉 Test completed!');
}
