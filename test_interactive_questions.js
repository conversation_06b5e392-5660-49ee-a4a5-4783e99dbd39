const axios = require('axios');
const chalk = require('chalk');

// Test server endpoint
const SERVER_URL = 'http://localhost:3000';

async function testInteractiveQuestions() {
    console.log(chalk.blue('\n🧮 Testing Interactive Math Questions Generation\n'));
    
    try {
        // Test generating questions with interactive types
        const response = await axios.post(`${SERVER_URL}/api/assess/math`, {
            level: 'intermediate',
            previousQuestions: [],
            studentId: 'test-interactive-' + Date.now(),
            includeInteractive: true
        });

        if (response.data && response.data.question) {
            const question = response.data.question;
            
            console.log(chalk.green('✅ Question Generated Successfully'));
            console.log(chalk.cyan('📊 Question Details:'));
            console.log(`   Type: ${question.type || 'standard'}`);
            console.log(`   Topic: ${question.topic || 'N/A'}`);
            console.log(`   Level: ${question.level || 'N/A'}`);
            console.log(`   Interactive: ${question.interactive ? 'Yes' : 'No'}`);
            
            if (question.interactive) {
                console.log(chalk.yellow('🎮 Interactive Question Data:'));
                console.log(`   Interactive Type: ${question.interactiveType}`);
                
                if (question.interactiveData) {
                    console.log('   Interactive Data:');
                    console.log(JSON.stringify(question.interactiveData, null, 4));
                }
            }
            
            console.log(chalk.white('\n📝 Question Text:'));
            console.log(`   ${question.question}`);
            
            if (question.options && question.options.length > 0) {
                console.log('\n📋 Options:');
                question.options.forEach((option, index) => {
                    console.log(`   ${String.fromCharCode(65 + index)}) ${option}`);
                });
            }
            
            console.log(`\n✅ Correct Answer: ${question.answer}`);
            
        } else {
            console.log(chalk.red('❌ No question data received'));
        }
        
    } catch (error) {
        console.log(chalk.red('❌ Error testing interactive questions:'));
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Error: ${error.response.data.error || error.response.data}`);
        } else {
            console.log(`   Error: ${error.message}`);
        }
    }
}

async function testMultipleInteractiveTypes() {
    console.log(chalk.blue('\n🔄 Testing Multiple Interactive Question Types\n'));
    
    const interactiveTypes = [
        'drag-drop',
        'number-line',
        'area-model',
        'equation-builder',
        'graph-plot',
        'sorting'
    ];
    
    for (const type of interactiveTypes) {
        try {
            console.log(chalk.yellow(`\n🎯 Testing ${type} questions...`));
            
            const response = await axios.post(`${SERVER_URL}/api/assess/math`, {
                level: 'intermediate',
                previousQuestions: [],
                studentId: 'test-' + type + '-' + Date.now(),
                includeInteractive: true,
                preferredInteractiveType: type
            });

            if (response.data && response.data.question) {
                const question = response.data.question;
                
                if (question.interactive && question.interactiveType === type) {
                    console.log(chalk.green(`   ✅ ${type} question generated successfully`));
                    console.log(`   📝 Question: ${question.question.substring(0, 100)}...`);
                } else if (question.interactive) {
                    console.log(chalk.orange(`   ⚠️  Generated ${question.interactiveType} instead of ${type}`));
                } else {
                    console.log(chalk.red(`   ❌ Standard question generated instead of ${type}`));
                }
            } else {
                console.log(chalk.red(`   ❌ No question generated for ${type}`));
            }
            
            // Small delay between requests
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.log(chalk.red(`   ❌ Error testing ${type}:`));
            if (error.response) {
                console.log(`      Status: ${error.response.status}`);
                console.log(`      Error: ${error.response.data.error || error.response.data}`);
            } else {
                console.log(`      Error: ${error.message}`);
            }
        }
    }
}

async function testQuestionValidation() {
    console.log(chalk.blue('\n🔍 Testing Question Validation\n'));
    
    // Test cases for validation
    const testCases = [
        {
            name: 'Valid Drag-Drop Question',
            question: {
                question: "Match the fractions to their decimal equivalents",
                type: "drag-drop",
                interactive: true,
                interactiveType: "drag-drop",
                interactiveData: {
                    items: ["1/2", "1/4", "3/4"],
                    targets: ["0.5", "0.25", "0.75"],
                    matches: [0, 1, 2]
                },
                answer: "1/2 → 0.5, 1/4 → 0.25, 3/4 → 0.75"
            }
        },
        {
            name: 'Valid Number Line Question',
            question: {
                question: "Place the number 2.5 on the number line",
                type: "number-line",
                interactive: true,
                interactiveType: "number-line",
                interactiveData: {
                    min: 0,
                    max: 5,
                    step: 0.5,
                    target: 2.5
                },
                answer: "2.5"
            }
        },
        {
            name: 'Invalid Interactive Question (missing data)',
            question: {
                question: "This is an interactive question",
                type: "drag-drop",
                interactive: true,
                interactiveType: "drag-drop",
                answer: "test"
            }
        }
    ];
    
    for (const testCase of testCases) {
        try {
            console.log(chalk.yellow(`\n🧪 Testing: ${testCase.name}`));
            
            // Simulate validation by sending to assessment endpoint
            const response = await axios.post(`${SERVER_URL}/api/assess/math/validate`, {
                question: testCase.question
            });
            
            if (response.data && response.data.valid) {
                console.log(chalk.green(`   ✅ Question passed validation`));
            } else {
                console.log(chalk.red(`   ❌ Question failed validation`));
                if (response.data.errors) {
                    console.log(`   Errors: ${response.data.errors.join(', ')}`);
                }
            }
            
        } catch (error) {
            console.log(chalk.red(`   ❌ Validation test failed:`));
            if (error.response && error.response.status === 404) {
                console.log('   (Validation endpoint not implemented - this is expected)');
            } else if (error.response) {
                console.log(`   Status: ${error.response.status}`);
                console.log(`   Error: ${error.response.data.error || error.response.data}`);
            } else {
                console.log(`   Error: ${error.message}`);
            }
        }
    }
}

async function runAllTests() {
    console.log(chalk.magenta('🚀 Starting Interactive Math Questions Tests'));
    console.log(chalk.gray('=' .repeat(60)));
    
    await testInteractiveQuestions();
    await testMultipleInteractiveTypes();
    await testQuestionValidation();
    
    console.log(chalk.magenta('\n🏁 Interactive Questions Testing Complete'));
    console.log(chalk.gray('=' .repeat(60)));
}

// Run tests if called directly
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testInteractiveQuestions,
    testMultipleInteractiveTypes,
    testQuestionValidation,
    runAllTests
};
