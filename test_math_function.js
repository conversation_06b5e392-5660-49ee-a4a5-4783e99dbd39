require('dotenv').config();
const OpenAI = require('openai');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');

// Copy the relevant functions from server.js to test them directly
const API_TIMEOUT_THRESHOLD = 25000; // 25 seconds
const MAX_RETRY_ATTEMPTS = 2;

const performanceMetrics = {
  totalRequests: 0,
  cacheHits: 0,
  cacheMisses: 0,
  fallbackUsage: 0,
  averageGenerationTime: 0,
  apiTimeouts: 0,
  retryAttempts: 0,
  successfulRetries: 0
};

// Mathematics Question Cache System
const mathQuestionCache = new Map();

function getMathQuestionSpecs(level) {
  const specs = {
    'Entry': { count: 10, maxScore: 10, timeLimit: 20 },
    'Level1': { count: 13, maxScore: 15, timeLimit: 25 },
    'GCSEPart1': { count: 15, maxScore: 20, timeLimit: 30 },
    'GCSEPart2': { count: 18, maxScore: 25, timeLimit: 35 }
  };
  return specs[level] || specs['Entry'];
}

function createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel) {
  return `Generate ${questionSpecs.count} mathematics questions for ${level} assessment.

Each question should:
- Be appropriate for adult learners at ${level} mathematics level
- Have 4 multiple choice options (A, B, C, D)
- Have exactly one correct answer
- Include a brief explanation
- Cover topics like: basic arithmetic, fractions, decimals, percentages, basic geometry, data handling, problem solving

Return as valid JSON array with this structure:
[
  {
    "id": 1,
    "type": "multiple_choice",
    "topic": "arithmetic",
    "question": "What is 15 + 27?",
    "options": ["40", "41", "42", "43"],
    "correctAnswer": "42",
    "points": 1,
    "explanation": "15 + 27 = 42"
  }
]

Generate exactly ${questionSpecs.count} questions with varied types and appropriate difficulty for ${level} level.`;
}

// Helper function to make OpenAI API calls with retry logic
async function makeOpenAICallWithRetry(prompt, level, attempt = 1) {
  console.log(`🔄 Making OpenAI API call (attempt ${attempt}/${MAX_RETRY_ATTEMPTS})...`);
  
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('API timeout')), API_TIMEOUT_THRESHOLD);
  });

  const apiPromise = openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content: "You are a mathematics assessment expert. Generate exactly the requested number of questions in valid JSON format. Be concise and accurate."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    max_tokens: 1500,
    temperature: 0.2
  });

  try {
    const completion = await Promise.race([apiPromise, timeoutPromise]);
    console.log(`✅ API call successful on attempt ${attempt}`);
    return completion;
  } catch (error) {
    console.log(`❌ API call failed on attempt ${attempt}:`, error.message);
    
    if (attempt < MAX_RETRY_ATTEMPTS && (error.message === 'API timeout' || error.code === 'rate_limit_exceeded')) {
      performanceMetrics.retryAttempts++;
      const backoffDelay = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s, 8s...
      console.log(`🔄 Retry attempt ${attempt + 1}/${MAX_RETRY_ATTEMPTS} after ${backoffDelay}ms delay...`);
      
      await new Promise(resolve => setTimeout(resolve, backoffDelay));
      return makeOpenAICallWithRetry(prompt, level, attempt + 1);
    }
    throw error;
  }
}

// Generate fallback questions
function generateEnhancedFallbackMathQuestions(level) {
  console.log(`⚠️  Using fallback questions for ${level}`);
  
  const fallbackQuestions = {
    'Level1': [
      {
        id: 1,
        type: "multiple_choice",
        topic: "arithmetic",
        question: "What is 25 + 17?",
        options: ["40", "41", "42", "43"],
        correctAnswer: "42",
        points: 1,
        explanation: "25 + 17 = 42"
      },
      {
        id: 2,
        type: "multiple_choice", 
        topic: "fractions",
        question: "What is 1/2 + 1/4?",
        options: ["1/6", "2/6", "3/4", "1/8"],
        correctAnswer: "3/4",
        points: 1,
        explanation: "1/2 + 1/4 = 2/4 + 1/4 = 3/4"
      }
    ]
  };
  
  const questions = fallbackQuestions[level] || fallbackQuestions['Level1'];
  const specs = getMathQuestionSpecs(level);
  
  // Repeat questions if we need more
  const result = [];
  for (let i = 0; i < specs.count; i++) {
    const questionIndex = i % questions.length;
    const question = { ...questions[questionIndex] };
    question.id = i + 1;
    result.push(question);
  }
  
  return result;
}

// Parse AI response (updated to handle markdown)
function parseAIQuestionsResponse(questionsText, level) {
  console.log('📝 Parsing AI response...');
  console.log('Response length:', questionsText.length);
  console.log('First 200 chars:', questionsText.substring(0, 200));

  try {
    let questions;

    // Strategy 1: Direct JSON parse (fastest)
    try {
      questions = JSON.parse(questionsText);
      if (Array.isArray(questions)) {
        console.log(`✅ Direct parse successful: ${questions.length} questions`);
        return questions;
      }
    } catch (e) {
      console.log('❌ Direct JSON parse failed, trying markdown cleanup...');
    }

    // Strategy 2: Remove markdown code blocks and try parsing
    let cleanedText = questionsText;
    // Remove ```json and ``` markers
    cleanedText = cleanedText.replace(/```json\s*/g, '').replace(/```\s*/g, '');
    // Remove any remaining backticks
    cleanedText = cleanedText.replace(/`/g, '');

    try {
      questions = JSON.parse(cleanedText);
      if (Array.isArray(questions)) {
        console.log(`✅ Markdown cleanup successful: ${questions.length} questions`);
        return questions;
      }
    } catch (e) {
      console.log('❌ Markdown cleanup failed, trying JSON extraction...');
    }

    // Strategy 3: Extract JSON array from cleaned text
    const jsonMatch = cleanedText.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      try {
        questions = JSON.parse(jsonMatch[0]);
        if (Array.isArray(questions)) {
          console.log(`✅ JSON extraction successful: ${questions.length} questions`);
          return questions;
        }
      } catch (extractError) {
        console.log('❌ JSON extraction failed');
      }
    }

    console.log('❌ All parsing attempts failed, using fallback');
    return generateEnhancedFallbackMathQuestions(level);

  } catch (error) {
    console.log('❌ Parsing error:', error.message);
    return generateEnhancedFallbackMathQuestions(level);
  }
}

// Main test function
async function testMathematicsQuestionGeneration(level = 'Level1', studentLevel = 'adult-learner') {
  console.log(`\n🧮 Testing Mathematics Question Generation for ${level}\n`);
  
  const startTime = Date.now();
  performanceMetrics.totalRequests++;

  try {
    console.log(`🔄 Cache miss - generating new questions with AI (timeout: ${API_TIMEOUT_THRESHOLD}ms)...`);
    const questionSpecs = getMathQuestionSpecs(level);
    console.log(`📋 Question specs: ${questionSpecs.count} questions, max score: ${questionSpecs.maxScore}`);
    const prompt = createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel);

    // Make API call with retry logic
    let completion;
    try {
      completion = await makeOpenAICallWithRetry(prompt, level);
      const generationTime = Date.now() - startTime;
      console.log(`✅ OpenAI API call successful in ${generationTime}ms`);
      
      // Track successful retry if this wasn't the first attempt
      if (performanceMetrics.retryAttempts > 0) {
        performanceMetrics.successfulRetries++;
      }
    } catch (error) {
      const generationTime = Date.now() - startTime;
      
      if (error.message === 'API timeout') {
        console.warn(`⏰ OpenAI API timeout after ${generationTime}ms (threshold: ${API_TIMEOUT_THRESHOLD}ms) - using fallback questions`);
        performanceMetrics.apiTimeouts++;
      } else {
        console.error(`❌ OpenAI API error after ${generationTime}ms:`, error.message);
        console.error('Full error details:', error);
      }
      
      const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
      return fallbackQuestions;
    }

    const questionsText = completion.choices[0].message.content;
    console.log('AI response received, parsing questions...');

    // Parse the response
    let questions = parseAIQuestionsResponse(questionsText, level);

    const generationTime = Date.now() - startTime;
    console.log(`Generated ${questions.length} mathematics questions for ${level} level in ${generationTime}ms`);

    return questions;

  } catch (error) {
    console.error('Error generating mathematics questions:', error);
    performanceMetrics.fallbackUsage++;
    const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
    return fallbackQuestions;
  }
}

// Run the test
async function runTest() {
  try {
    const questions = await testMathematicsQuestionGeneration('Level1', 'adult-learner');
    
    console.log('\n📊 Results Summary:');
    console.log('===================');
    console.log('Total questions:', questions.length);
    console.log('Performance metrics:', performanceMetrics);
    
    if (questions.length > 0) {
      console.log('\n📝 Sample Questions:');
      console.log('First question:', questions[0]);
      if (questions.length > 1) {
        console.log('Second question:', questions[1]);
      }
      
      // Check if questions look AI-generated
      const hasVariedContent = questions.some(q => 
        q.topic && q.explanation && q.question.length > 20
      );
      
      if (hasVariedContent) {
        console.log('\n✅ SUCCESS: Questions appear to be AI-generated!');
      } else {
        console.log('\n⚠️  WARNING: Questions appear to be fallback/hardcoded');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

console.log('🧮 Mathematics Question Generation Test\n');
runTest();
