/**
 * Digital Skills Assessment Module
 * Handles the digital skills assessment flow for student users
 */

class DigitalSkillsAssessment {
  constructor() {
    this.currentLevel = 'EntryLevel2';
    this.timeLimit = 25 * 60; // Default 25 minutes in seconds
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;
    this.isSubmitted = false;
    
    // Assessment data
    this.questions = [];
    this.currentQuestionIndex = 0;
    this.answers = [];
    this.assessmentId = null;
    
    // Assessment metadata
    this.assessmentStartTime = null;
    this.questionStartTimes = [];

    // Performance tracking
    this.performanceData = {};
    this.interactionLogs = {};
    this.currentQuestionPerformance = null;
    
    // Level specifications
    this.levelSpecs = {
      'EntryLevel2': { 
        timeLimit: 25 * 60, 
        questionCount: 15, 
        passingScore: 12, 
        maxScore: 30,
        name: "Computer Skills Beginners"
      },
      'EntryLevel2Plus': { 
        timeLimit: 30 * 60, 
        questionCount: 18, 
        passingScore: 15, 
        maxScore: 36,
        name: "Computer Skills Beginners Plus"
      },
      'Level1': { 
        timeLimit: 35 * 60, 
        questionCount: 20, 
        passingScore: 18, 
        maxScore: 40,
        name: "Computer Skills for Everyday Life"
      },
      'Level2': { 
        timeLimit: 40 * 60, 
        questionCount: 22, 
        passingScore: 20, 
        maxScore: 44,
        name: "Computer Skills for Work"
      },
      'EntryLevel3': { 
        timeLimit: 30 * 60, 
        questionCount: 16, 
        passingScore: 14, 
        maxScore: 32,
        name: "Improvers Plus"
      },
      'ICDLLevel2': { 
        timeLimit: 45 * 60, 
        questionCount: 25, 
        passingScore: 22, 
        maxScore: 50,
        name: "ICDL Level 2"
      },
      'ICDLLevel3': { 
        timeLimit: 50 * 60, 
        questionCount: 30, 
        passingScore: 26, 
        maxScore: 60,
        name: "ICDL Level 3"
      }
    };
  }

  /**
   * Initialize the digital skills assessment
   */
  async init() {
    this.setupEventListeners();
    console.log('Digital skills assessment initialized');

    // Auto-initialize if page is loaded directly
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.autoInit());
    } else {
      this.autoInit();
    }
  }

  /**
   * Auto-initialize if page is loaded directly
   */
  autoInit() {
    // Check if we're on the digital skills assessment page
    if (window.location.pathname.includes('digitalSkillsAssessment.html')) {
      console.log('Auto-initializing digital skills assessment');

      // Check for stored user data from SGA.html
      const storedUserData = localStorage.getItem('digitalSkillsUserData');
      if (storedUserData) {
        try {
          const userData = JSON.parse(storedUserData);
          console.log('Found stored user data:', userData);

          // Pre-fill form with stored data
          this.prefillForm(userData);

          // Clear stored data
          localStorage.removeItem('digitalSkillsUserData');

          // Auto-submit form to start assessment
          setTimeout(() => {
            this.handleFormSubmission();
          }, 500);

        } catch (error) {
          console.error('Error parsing stored user data:', error);
          this.setupFormSubmission();
        }
      } else {
        this.setupFormSubmission();
      }
    }
  }

  /**
   * Pre-fill form with user data
   */
  prefillForm(userData) {
    if (userData.firstName) {
      const firstNameField = document.getElementById('first-name');
      if (firstNameField) firstNameField.value = userData.firstName;
    }

    if (userData.lastName) {
      const lastNameField = document.getElementById('last-name');
      if (lastNameField) lastNameField.value = userData.lastName;
    }

    if (userData.email) {
      const emailField = document.getElementById('email');
      if (emailField) emailField.value = userData.email;
    }

    if (userData.studentLevel) {
      const studentLevelField = document.getElementById('student-level');
      if (studentLevelField) studentLevelField.value = userData.studentLevel;
    }

    // Store user data for later use
    this.userData = userData;
  }

  /**
   * Set up event listeners for the assessment
   */
  setupEventListeners() {
    // Form submission
    this.setupFormSubmission();

    // Begin assessment button
    const beginButton = document.getElementById('begin-assessment');
    if (beginButton) {
      beginButton.addEventListener('click', () => this.startAssessment());
    }

    // Navigation buttons
    const prevButton = document.getElementById('prev-question');
    if (prevButton) {
      prevButton.addEventListener('click', () => this.navigateQuestion(-1));
    }

    const nextButton = document.getElementById('next-question');
    if (nextButton) {
      nextButton.addEventListener('click', () => this.navigateQuestion(1));
    }

    // Submit assessment button
    const submitButton = document.getElementById('submit-assessment');
    if (submitButton) {
      submitButton.addEventListener('click', () => this.confirmSubmit());
    }

    // Results actions
    const viewReportButton = document.getElementById('view-detailed-report');
    if (viewReportButton) {
      viewReportButton.addEventListener('click', () => this.showDetailedReport());
    }

    const retakeButton = document.getElementById('retake-assessment');
    if (retakeButton) {
      retakeButton.addEventListener('click', () => this.retakeAssessment());
    }

    const nextLevelButton = document.getElementById('next-level');
    if (nextLevelButton) {
      nextLevelButton.addEventListener('click', () => this.progressToNextLevel());
    }
  }

  /**
   * Set up form submission handler
   */
  setupFormSubmission() {
    const form = document.getElementById('user-form');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleFormSubmission();
      });
    }
  }

  /**
   * Handle form submission
   */
  async handleFormSubmission() {
    try {
      // Get form data
      const firstName = document.getElementById('first-name').value;
      const lastName = document.getElementById('last-name').value;
      const email = document.getElementById('email').value;
      const studentLevel = document.getElementById('student-level').value;
      
      // Get selected level
      const levelRadios = document.getElementsByName('assessment-level');
      let selectedLevel = 'EntryLevel2'; // Default
      
      for (const radio of levelRadios) {
        if (radio.checked) {
          selectedLevel = radio.value;
          break;
        }
      }
      
      // Validate form data
      if (!firstName || !lastName || !email || !studentLevel) {
        alert('Please fill in all required fields');
        return;
      }
      
      // Basic email validation
      if (!this.validateEmail(email)) {
        alert('Please enter a valid email address');
        return;
      }
      
      // Store user data
      this.userData = {
        firstName,
        lastName,
        name: `${firstName} ${lastName}`,
        email,
        studentLevel,
        userType: 'student'
      };
      
      // Set current level
      this.currentLevel = selectedLevel;
      
      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;
      
      // Hide form and show instructions
      this.hideContainerWithTransition('user-form-container', () => {
        this.showInstructions();
        document.getElementById('header').classList.remove('hidden');
      });
      
    } catch (error) {
      console.error('Error handling form submission:', error);
      alert('An error occurred. Please try again.');
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  }

  /**
   * Show assessment instructions
   */
  showInstructions() {
    // Hide all other screens first
    this.hideAllAssessmentScreens();
    
    // Show digital skills assessment container
    document.getElementById('digital-skills-assessment-container').classList.remove('hidden');
    
    // Show only instructions container
    document.getElementById('assessment-instructions').classList.remove('hidden');
    
    // Update instruction content
    const specs = this.levelSpecs[this.currentLevel];
    document.getElementById('time-limit-display').textContent = `${Math.floor(specs.timeLimit / 60)} minutes`;
    document.getElementById('question-count-display').textContent = `${specs.questionCount} questions`;
    document.getElementById('passing-score-display').textContent = `${specs.passingScore} points`;
    
    // Update header
    document.getElementById('current-level').textContent = this.currentLevel;
    document.getElementById('total-questions').textContent = specs.questionCount;
    document.getElementById('timer-display').textContent = this.formatTime(specs.timeLimit);
  }

  /**
   * Hide all assessment screens
   */
  hideAllAssessmentScreens() {
    const screens = document.querySelectorAll('.assessment-screen');
    screens.forEach(screen => {
      screen.classList.add('hidden');
    });
  }

  /**
   * Hide container with transition
   */
  hideContainerWithTransition(containerId, callback) {
    const container = document.getElementById(containerId);
    if (container) {
      container.style.opacity = '0';
      container.style.transform = 'translateY(-20px)';
      
      setTimeout(() => {
        container.classList.add('hidden');
        container.style.opacity = '';
        container.style.transform = '';
        
        if (callback) callback();
      }, 300);
    } else {
      if (callback) callback();
    }
  }

  /**
   * Show container with transition
   */
  showContainerWithTransition(containerId, displayType = 'block') {
    const container = document.getElementById(containerId);
    if (container) {
      container.style.opacity = '0';
      container.style.transform = 'translateY(20px)';
      container.style.display = displayType;
      container.classList.remove('hidden');
      
      // Force reflow
      container.offsetHeight;
      
      container.style.opacity = '1';
      container.style.transform = 'translateY(0)';
    }
  }

  /**
   * Format time in MM:SS format
   */
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Start the assessment by fetching questions
   */
  async startAssessment() {
    try {
      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3003'
        : window.location.origin;

      // Show loading overlay
      this.showLoadingOverlay('Generating Questions...', 'Please wait while we create your digital skills assessment');
      this.updateLoadingProgress(25, 'Connecting to assessment server...');

      const response = await fetch(`${baseUrl}/api/digital-skills-assessments/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: this.currentLevel,
          email: this.userData.email,
          studentLevel: this.userData.studentLevel
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      this.updateLoadingProgress(75, 'Processing questions...');

      const data = await response.json();

      this.questions = data.questions;
      this.assessmentId = data.assessmentId;
      this.timeLimit = data.timeLimit;
      this.timeRemaining = data.timeLimit;

      // Initialize answers array
      this.answers = new Array(this.questions.length).fill(null);

      // Initialize performance tracking
      this.assessmentStartTime = Date.now();
      this.questionStartTimes = new Array(this.questions.length).fill(null);

      this.updateLoadingProgress(100, 'Starting assessment...');

      setTimeout(() => {
        this.hideLoadingOverlay();
        this.showQuestions();
        this.startTimer();
      }, 500);

    } catch (error) {
      console.error('Error starting digital skills assessment:', error);
      this.hideLoadingOverlay();
      alert('Failed to start assessment. Please try again.');
    }
  }

  /**
   * Show loading overlay
   */
  showLoadingOverlay(title, message) {
    const overlay = document.getElementById('loading-overlay');
    const titleElement = document.getElementById('loading-title');
    const messageElement = document.getElementById('loading-message');

    if (overlay) {
      overlay.classList.remove('hidden');
      if (titleElement) titleElement.textContent = title;
      if (messageElement) messageElement.textContent = message;
    }
  }

  /**
   * Hide loading overlay
   */
  hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.classList.add('hidden');
    }
  }

  /**
   * Update loading progress
   */
  updateLoadingProgress(percentage, message) {
    const progressFill = document.getElementById('loading-progress-fill');
    const progressText = document.getElementById('loading-progress-text');
    const messageElement = document.getElementById('loading-message');

    if (progressFill) {
      progressFill.style.width = `${percentage}%`;
    }

    if (progressText) {
      progressText.textContent = `${percentage}%`;
    }

    if (messageElement && message) {
      messageElement.textContent = message;
    }
  }

  /**
   * Show the questions interface
   */
  showQuestions() {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show only questions container
    document.getElementById('assessment-questions').classList.remove('hidden');

    // Load first question
    this.loadCurrentQuestion();
  }

  /**
   * Load the current question
   */
  loadCurrentQuestion() {
    if (this.currentQuestionIndex >= this.questions.length) {
      this.completeAssessment();
      return;
    }

    const question = this.questions[this.currentQuestionIndex];

    // Record question start time
    if (!this.questionStartTimes[this.currentQuestionIndex]) {
      this.questionStartTimes[this.currentQuestionIndex] = Date.now();
    }

    // Update question display
    document.getElementById('question-title').textContent = question.question;

    // Update progress
    const progressText = document.getElementById('progress-text');
    const progressFill = document.getElementById('progress-fill');
    const currentQuestion = document.getElementById('current-question');

    if (progressText) {
      progressText.textContent = `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;
    }

    if (progressFill) {
      const percentage = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
      progressFill.style.width = `${percentage}%`;
    }

    if (currentQuestion) {
      currentQuestion.textContent = this.currentQuestionIndex + 1;
    }

    // Create options
    this.createQuestionOptions(question);

    // Update navigation buttons
    this.updateNavigationButtons();
  }

  /**
   * Create question options
   */
  createQuestionOptions(question) {
    const optionsContainer = document.getElementById('question-options');
    if (!optionsContainer) return;

    optionsContainer.innerHTML = '';

    question.options.forEach((option, index) => {
      const optionElement = document.createElement('div');
      optionElement.className = 'question-option';

      const optionId = `option-${this.currentQuestionIndex}-${index}`;

      optionElement.innerHTML = `
        <input type="radio" id="${optionId}" name="question-${this.currentQuestionIndex}" value="${option}">
        <label for="${optionId}" class="option-label">
          <span class="option-letter">${String.fromCharCode(65 + index)}</span>
          <span class="option-text">${option}</span>
        </label>
      `;

      optionsContainer.appendChild(optionElement);

      // Add event listener
      const radioInput = optionElement.querySelector('input[type="radio"]');
      radioInput.addEventListener('change', () => {
        this.handleAnswerSelection(option);
      });
    });

    // Restore previous answer if exists
    const previousAnswer = this.answers[this.currentQuestionIndex];
    if (previousAnswer) {
      const radioInput = optionsContainer.querySelector(`input[value="${previousAnswer}"]`);
      if (radioInput) {
        radioInput.checked = true;
      }
    }
  }

  /**
   * Handle answer selection
   */
  handleAnswerSelection(answer) {
    this.answers[this.currentQuestionIndex] = answer;

    // Record interaction
    this.recordInteraction('answer_selected', {
      questionIndex: this.currentQuestionIndex,
      answer: answer,
      timestamp: Date.now()
    });

    // Update navigation buttons
    this.updateNavigationButtons();
  }

  /**
   * Record user interaction
   */
  recordInteraction(type, details) {
    if (!this.interactionLogs[this.currentQuestionIndex]) {
      this.interactionLogs[this.currentQuestionIndex] = [];
    }

    this.interactionLogs[this.currentQuestionIndex].push({
      type,
      details,
      timestamp: Date.now()
    });
  }

  /**
   * Update navigation buttons
   */
  updateNavigationButtons() {
    const prevButton = document.getElementById('prev-question');
    const nextButton = document.getElementById('next-question');
    const submitButton = document.getElementById('submit-assessment');

    // Previous button
    if (prevButton) {
      prevButton.disabled = this.currentQuestionIndex === 0;
    }

    // Next/Submit button logic
    const isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;

    if (isLastQuestion) {
      if (nextButton) nextButton.classList.add('hidden');
      if (submitButton) submitButton.classList.remove('hidden');
    } else {
      if (nextButton) nextButton.classList.remove('hidden');
      if (submitButton) submitButton.classList.add('hidden');
    }
  }

  /**
   * Navigate between questions
   */
  navigateQuestion(direction) {
    const newIndex = this.currentQuestionIndex + direction;

    if (newIndex >= 0 && newIndex < this.questions.length) {
      this.currentQuestionIndex = newIndex;
      this.loadCurrentQuestion();
    }
  }

  /**
   * Start the assessment timer
   */
  startTimer() {
    this.timerInterval = setInterval(() => {
      this.timeRemaining--;

      // Update timer display
      const timerDisplay = document.getElementById('timer-display');
      if (timerDisplay) {
        timerDisplay.textContent = this.formatTime(this.timeRemaining);

        // Add warning class when time is running low
        if (this.timeRemaining <= 300) { // 5 minutes
          timerDisplay.classList.add('timer-warning');
        }

        if (this.timeRemaining <= 60) { // 1 minute
          timerDisplay.classList.add('timer-critical');
        }
      }

      // Auto-submit when time runs out
      if (this.timeRemaining <= 0) {
        this.timeUp();
      }
    }, 1000);
  }

  /**
   * Handle time up scenario
   */
  timeUp() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }

    alert('Time is up! Your assessment will be submitted automatically.');
    this.submitAssessment();
  }

  /**
   * Confirm assessment submission
   */
  confirmSubmit() {
    // Count answered questions
    const answeredCount = this.answers.filter(answer => answer !== null).length;
    const totalQuestions = this.questions.length;

    if (answeredCount < totalQuestions) {
      const unanswered = totalQuestions - answeredCount;
      const confirmMessage = `You have ${unanswered} unanswered question${unanswered > 1 ? 's' : ''}. Are you sure you want to submit?`;

      if (!confirm(confirmMessage)) {
        return;
      }
    }

    this.submitAssessment();
  }

  /**
   * Submit assessment for grading
   */
  async submitAssessment() {
    if (this.isSubmitted) {
      return; // Prevent double submission
    }

    this.isSubmitted = true;

    try {
      // Stop timer
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      }

      // Show loading
      this.showLoadingOverlay('Submitting Assessment...', 'Please wait while we analyze your responses');
      this.updateLoadingProgress(25, 'Processing answers...');

      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3003'
        : window.location.origin;

      const timeSpent = this.timeLimit - this.timeRemaining;

      // Prepare assessment data
      const assessmentData = {
        answers: this.answers.map((answer, index) => ({
          questionId: this.questions[index].id,
          answer: answer || '', // Use empty string for unanswered questions
          timeSpent: this.questionStartTimes[index] ? Date.now() - this.questionStartTimes[index] : 0
        })),
        email: this.userData.email,
        level: this.currentLevel,
        timeSpent: timeSpent,
        userData: {
          firstName: this.userData.firstName,
          lastName: this.userData.lastName,
          name: this.userData.name,
          studentLevel: this.userData.studentLevel,
          userType: 'student'
        },
        detailedResponses: {
          assessmentMetadata: {
            startTime: this.assessmentStartTime,
            endTime: Date.now(),
            userAgent: navigator.userAgent,
            sessionId: this.assessmentId
          },
          interactionLogs: this.interactionLogs
        }
      };

      this.updateLoadingProgress(50, 'Analyzing performance...');

      const response = await fetch(`${baseUrl}/api/digital-skills-assessments/${this.assessmentId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assessmentData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      this.updateLoadingProgress(75, 'Generating results...');

      const results = await response.json();

      this.updateLoadingProgress(100, 'Complete!');

      setTimeout(() => {
        this.hideLoadingOverlay();
        this.showResults(results);
      }, 500);

    } catch (error) {
      console.error('Error submitting digital skills assessment:', error);
      this.hideLoadingOverlay();
      this.isSubmitted = false; // Allow retry
      alert('Failed to submit assessment. Please try again.');
    }
  }

  /**
   * Show assessment results
   */
  showResults(results) {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show results container
    document.getElementById('assessment-results').classList.remove('hidden');

    // Update results display
    const resultsTitle = document.getElementById('results-title');
    const resultsScore = document.getElementById('results-score');
    const resultsContent = document.getElementById('results-content');

    if (resultsTitle) {
      resultsTitle.textContent = results.passed ?
        `Congratulations! You passed ${this.levelSpecs[this.currentLevel].name}!` :
        `Assessment Complete - ${this.levelSpecs[this.currentLevel].name}`;
    }

    if (resultsScore) {
      const scoreValue = resultsScore.querySelector('.score-value');
      const scoreTotal = resultsScore.querySelector('.score-total');

      if (scoreValue) scoreValue.textContent = results.score;
      if (scoreTotal) scoreTotal.textContent = `/ ${results.maxScore}`;

      // Add pass/fail styling
      resultsScore.className = `results-score ${results.passed ? 'passed' : 'needs-improvement'}`;
    }

    // Create detailed results content
    this.createResultsContent(results, resultsContent);

    // Show appropriate action buttons
    this.updateResultsActions(results);
  }

  /**
   * Create detailed results content
   */
  createResultsContent(results, container) {
    if (!container) return;

    const passedClass = results.passed ? 'passed' : 'needs-improvement';
    const statusText = results.passed ? 'Passed' : 'Needs Improvement';
    const statusIcon = results.passed ? '✅' : '📚';

    container.innerHTML = `
      <div class="results-summary ${passedClass}">
        <div class="status-badge">
          <span class="status-icon">${statusIcon}</span>
          <span class="status-text">${statusText}</span>
        </div>
        <p class="results-feedback">${results.feedback || 'Assessment completed successfully.'}</p>
      </div>

      <div class="results-details">
        <div class="performance-metrics">
          <div class="metric">
            <span class="metric-label">Score</span>
            <span class="metric-value">${results.score}/${results.maxScore}</span>
          </div>
          <div class="metric">
            <span class="metric-label">Percentage</span>
            <span class="metric-value">${Math.round((results.score / results.maxScore) * 100)}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">Time Taken</span>
            <span class="metric-value">${this.formatTime(this.timeLimit - this.timeRemaining)}</span>
          </div>
        </div>

        ${this.createTopicBreakdown(results.topicBreakdown)}
        ${this.createRecommendations(results)}
      </div>
    `;
  }

  /**
   * Create topic breakdown display
   */
  createTopicBreakdown(topicBreakdown) {
    if (!topicBreakdown || Object.keys(topicBreakdown).length === 0) {
      return '';
    }

    const topicNames = {
      computerBasics: 'Computer Basics',
      mouseKeyboard: 'Mouse & Keyboard',
      basicOperations: 'Basic Operations',
      fileManagement: 'File Management',
      basicSafety: 'Basic Safety',
      laptopDesktop: 'Laptop & Desktop',
      applications: 'Applications',
      internetSafety: 'Internet Safety',
      emailBasics: 'Email Basics',
      digitalCitizenship: 'Digital Citizenship',
      microsoftApps: 'Microsoft Apps',
      onlineBanking: 'Online Banking',
      cloudStorage: 'Cloud Storage',
      digitalIdentity: 'Digital Identity',
      internetSkills: 'Internet Skills',
      advancedFormatting: 'Advanced Formatting',
      spreadsheets: 'Spreadsheets',
      presentations: 'Presentations',
      workplaceSkills: 'Workplace Skills',
      collaboration: 'Collaboration',
      operatingSystems: 'Operating Systems',
      emailProficiency: 'Email Proficiency',
      onlineTransactions: 'Online Transactions',
      digitalSafety: 'Digital Safety',
      troubleshooting: 'Troubleshooting',
      timedExam: 'Timed Exam',
      wordAdvanced: 'Advanced Word',
      excelAdvanced: 'Advanced Excel',
      powerpointAdvanced: 'Advanced PowerPoint',
      employmentSkills: 'Employment Skills',
      advancedMicrosoft: 'Advanced Microsoft',
      itCareers: 'IT Careers',
      higherEducation: 'Higher Education',
      professionalSkills: 'Professional Skills',
      certification: 'Certification'
    };

    const topicItems = Object.entries(topicBreakdown).map(([topic, data]) => {
      const topicName = topicNames[topic] || topic;
      const percentage = data.percentage || 0;
      const performanceClass = percentage >= 70 ? 'good' : percentage >= 50 ? 'average' : 'needs-work';

      return `
        <div class="topic-item ${performanceClass}">
          <div class="topic-header">
            <span class="topic-name">${topicName}</span>
            <span class="topic-score">${data.correct}/${data.total} (${percentage}%)</span>
          </div>
          <div class="topic-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${percentage}%"></div>
            </div>
          </div>
        </div>
      `;
    }).join('');

    return `
      <div class="topic-breakdown">
        <h4 class="breakdown-title">Performance by Topic</h4>
        <div class="topic-list">
          ${topicItems}
        </div>
      </div>
    `;
  }

  /**
   * Create recommendations display
   */
  createRecommendations(results) {
    const strengths = results.strengths || [];
    const improvements = results.improvements || [];
    const recommendations = results.recommendations || [];
    const nextSteps = results.nextSteps || [];

    let content = '';

    if (strengths.length > 0) {
      content += `
        <div class="recommendations-section strengths">
          <h4 class="section-title">💪 Strengths</h4>
          <ul class="recommendation-list">
            ${strengths.map(strength => `<li>${strength}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    if (improvements.length > 0) {
      content += `
        <div class="recommendations-section improvements">
          <h4 class="section-title">📈 Areas for Improvement</h4>
          <ul class="recommendation-list">
            ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    if (recommendations.length > 0) {
      content += `
        <div class="recommendations-section recommendations">
          <h4 class="section-title">💡 Recommendations</h4>
          <ul class="recommendation-list">
            ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    if (nextSteps.length > 0) {
      content += `
        <div class="recommendations-section next-steps">
          <h4 class="section-title">🎯 Next Steps</h4>
          <ul class="recommendation-list">
            ${nextSteps.map(step => `<li>${step}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    return content;
  }

  /**
   * Update results action buttons
   */
  updateResultsActions(results) {
    const nextLevelButton = document.getElementById('next-level');

    // Show next level button if passed and not at highest level
    if (results.passed && nextLevelButton) {
      const currentLevelIndex = Object.keys(this.levelSpecs).indexOf(this.currentLevel);
      const hasNextLevel = currentLevelIndex < Object.keys(this.levelSpecs).length - 1;

      if (hasNextLevel) {
        nextLevelButton.classList.remove('hidden');
        const nextLevel = Object.keys(this.levelSpecs)[currentLevelIndex + 1];
        const nextLevelName = this.levelSpecs[nextLevel].name;
        nextLevelButton.querySelector('.btn-text').textContent = `Try ${nextLevelName}`;
      }
    }
  }

  /**
   * Show detailed report (placeholder for future implementation)
   */
  showDetailedReport() {
    alert('Detailed report functionality will be implemented in a future update.');
  }

  /**
   * Retake assessment
   */
  retakeAssessment() {
    if (confirm('Are you sure you want to retake this assessment? Your current results will be lost.')) {
      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;
      this.isSubmitted = false;
      this.timeRemaining = this.timeLimit;

      // Clear performance data
      this.performanceData = {};
      this.interactionLogs = {};
      this.questionStartTimes = [];

      // Show instructions again
      this.showInstructions();
    }
  }

  /**
   * Progress to next level
   */
  async progressToNextLevel() {
    const currentLevelIndex = Object.keys(this.levelSpecs).indexOf(this.currentLevel);
    const nextLevel = Object.keys(this.levelSpecs)[currentLevelIndex + 1];

    if (!nextLevel) {
      alert('You have completed the highest level available!');
      return;
    }

    try {
      // Update current level
      this.currentLevel = nextLevel;

      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;

      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;
      this.isSubmitted = false;

      // Clear performance data
      this.performanceData = {};
      this.interactionLogs = {};
      this.questionStartTimes = [];

      // Show instructions for new level
      this.showInstructions();

    } catch (error) {
      console.error('Error progressing to next level:', error);
      alert('Failed to progress to next level. Please try again.');
    }
  }
}
