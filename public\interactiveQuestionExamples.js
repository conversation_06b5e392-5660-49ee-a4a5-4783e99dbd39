/**
 * Interactive Question Examples for Mathematics Assessment
 * These hardcoded examples demonstrate the interactive question types
 * before AI generation is implemented.
 */

const INTERACTIVE_QUESTION_EXAMPLES = {
  // Entry Level Interactive Questions (22 total questions)
  Entry: [
    // Number Line Questions
    {
      id: 1,
      type: "number-line",
      topic: "fractions",
      question: "Place the fraction 1/2 on the number line.",
      numberLineConfig: {
        min: 0,
        max: 1,
        step: 0.1,
        snapToGrid: true
      },
      correctAnswer: "0.5",
      points: 2,
      explanation: "1/2 is equivalent to 0.5, which is halfway between 0 and 1."
    },
    {
      id: 2,
      type: "number-line",
      topic: "decimals",
      question: "Place 0.75 on the number line.",
      numberLineConfig: {
        min: 0,
        max: 1,
        step: 0.05,
        snapToGrid: true
      },
      correctAnswer: "0.75",
      points: 2,
      explanation: "0.75 is three-quarters of the way from 0 to 1."
    },
    {
      id: 3,
      type: "number-line",
      topic: "integers",
      question: "Place -3 on the number line.",
      numberLineConfig: {
        min: -5,
        max: 5,
        step: 1,
        snapToGrid: true
      },
      correctAnswer: "-3",
      points: 2,
      explanation: "-3 is three units to the left of zero."
    },

    // Drag and Drop Questions
    {
      id: 4,
      type: "drag-drop",
      topic: "fractions",
      question: "Match each fraction to its decimal equivalent.",
      dragDropConfig: {
        items: [
          { id: "frac1", text: "1/4" },
          { id: "frac2", text: "1/2" },
          { id: "frac3", text: "3/4" }
        ],
        zones: [
          { id: "dec1", label: "0.25" },
          { id: "dec2", label: "0.50" },
          { id: "dec3", label: "0.75" }
        ]
      },
      correctAnswer: '{"0.25":"1/4","0.50":"1/2","0.75":"3/4"}',
      points: 2,
      explanation: "1/4 = 0.25, 1/2 = 0.50, 3/4 = 0.75"
    },
    {
      id: 5,
      type: "drag-drop",
      topic: "percentages",
      question: "Match each percentage to its fraction equivalent.",
      dragDropConfig: {
        items: [
          { id: "perc1", text: "25%" },
          { id: "perc2", text: "50%" },
          { id: "perc3", text: "75%" }
        ],
        zones: [
          { id: "frac1", label: "1/4" },
          { id: "frac2", label: "1/2" },
          { id: "frac3", label: "3/4" }
        ]
      },
      correctAnswer: '{"1/4":"25%","1/2":"50%","3/4":"75%"}',
      points: 2,
      explanation: "25% = 1/4, 50% = 1/2, 75% = 3/4"
    },

    // Number Line Questions (Additional)
    {
      id: 6,
      type: "number-line",
      topic: "percentages",
      question: "Place 25% on the number line (as a decimal).",
      numberLineConfig: {
        min: 0,
        max: 1,
        step: 0.01, // Optimized for precise decimal placement
        snapToGrid: true,
        showLabels: true, // Enhanced for better user experience
        labelStep: 0.25 // Show labels at 0, 0.25, 0.5, 0.75, 1
      },
      correctAnswer: "0.25",
      points: 2,
      explanation: "25% = 25/100 = 0.25"
    },
    {
      id: 7,
      type: "area-model",
      topic: "fractions",
      question: "Shade 3/4 of the fraction bar.",
      areaModelConfig: {
        fractionBars: [
          { segments: 4, label: "Quarters Bar" }
        ],
        geometricShapes: []
      },
      correctAnswer: '{"fractionBars":{"Quarters Bar":{"totalSegments":4,"shadedSegments":[0,1,2],"fraction":"3/4"}},"geometricShapes":[]}',
      points: 2,
      explanation: "To show 3/4, shade 3 out of 4 equal parts."
    },
    {
      id: 8,
      type: "area-model",
      topic: "geometry",
      question: "Click on the shapes that represent 1/2 of a whole.",
      areaModelConfig: {
        fractionBars: [],
        geometricShapes: [
          { type: "rectangle", width: 100, height: 50, label: "Rectangle" },
          { type: "circle", width: 80, height: 80, label: "Circle" },
          { type: "triangle", width: 90, height: 80, label: "Triangle" }
        ]
      },
      correctAnswer: '{"fractionBars":{},"geometricShapes":[{"type":"rectangle","label":"Rectangle","shaded":true}]}',
      points: 2,
      explanation: "Any shape can represent 1/2 when half of it is shaded."
    }
  ],

  // Level 1 Interactive Questions (13 total questions)
  Level1: [
    {
      id: 1,
      type: "number-line",
      topic: "advancedArithmetic",
      question: "Place -2.5 on the number line.",
      numberLineConfig: {
        min: -5,
        max: 5,
        step: 0.5,
        snapToGrid: true
      },
      correctAnswer: "-2.5",
      points: 2,
      explanation: "-2.5 is halfway between -3 and -2."
    },
    {
      id: 2,
      type: "drag-drop",
      topic: "algebraicExpressions",
      question: "Match each algebraic expression to its simplified form.",
      dragDropConfig: {
        items: [
          { id: "expr1", text: "2x + 3x" },
          { id: "expr2", text: "4y - 2y" },
          { id: "expr3", text: "3a + 2a - a" }
        ],
        zones: [
          { id: "simp1", label: "5x" },
          { id: "simp2", label: "2y" },
          { id: "simp3", label: "4a" }
        ]
      },
      correctAnswer: '{"5x":"2x + 3x","2y":"4y - 2y","4a":"3a + 2a - a"}',
      points: 2,
      explanation: "Combine like terms: 2x + 3x = 5x, 4y - 2y = 2y, 3a + 2a - a = 4a"
    },
    {
      id: 3,
      type: "drag-drop",
      topic: "fractionsDecimals",
      question: "Match each percentage to its fraction equivalent.",
      dragDropConfig: {
        items: [
          { id: "perc1", text: "50%" },
          { id: "perc2", text: "25%" },
          { id: "perc3", text: "75%" }
        ],
        zones: [
          { id: "frac1", label: "1/2" },
          { id: "frac2", label: "1/4" },
          { id: "frac3", label: "3/4" }
        ]
      },
      correctAnswer: '{"1/2":"50%","1/4":"25%","3/4":"75%"}',
      points: 2,
      explanation: "50% = 1/2, 25% = 1/4, 75% = 3/4"
    }
  ],

  // GCSE Part 1 Interactive Questions (7 total questions)
  GCSEPart1: [
    {
      id: 1,
      type: "number-line",
      topic: "numberOperations",
      question: "Place √9 on the number line.",
      numberLineConfig: {
        min: 0,
        max: 10,
        step: 1,
        snapToGrid: true
      },
      correctAnswer: "3",
      points: 2,
      explanation: "√9 = 3 because 3² = 9"
    },
    {
      id: 2,
      type: "drag-drop",
      topic: "algebra",
      question: "Match each equation to its solution.",
      dragDropConfig: {
        items: [
          { id: "eq1", text: "2x = 10" },
          { id: "eq2", text: "x + 3 = 8" },
          { id: "eq3", text: "3x - 1 = 14" }
        ],
        zones: [
          { id: "sol1", label: "x = 5" },
          { id: "sol2", label: "x = 5" },
          { id: "sol3", label: "x = 5" }
        ]
      },
      correctAnswer: '{"x = 5":"2x = 10"}',
      points: 2,
      explanation: "Solve each equation by isolating x."
    }
  ],

  // GCSE Part 2 Interactive Questions (10 total questions)
  GCSEPart2: [
    {
      id: 1,
      type: "number-line",
      topic: "complexCalculations",
      question: "Place the result of 2.5 × 1.6 on the number line.",
      numberLineConfig: {
        min: 0,
        max: 10,
        step: 0.1,
        snapToGrid: true
      },
      correctAnswer: "4.0",
      points: 2,
      explanation: "2.5 × 1.6 = 4.0"
    },
    {
      id: 2,
      type: "number-line",
      topic: "algebra",
      question: "Place the solution to 3x + 2 = 11 on the number line.",
      numberLineConfig: {
        min: 0,
        max: 10,
        step: 1,
        snapToGrid: true
      },
      correctAnswer: "3",
      points: 2,
      explanation: "3x + 2 = 11, so 3x = 9, therefore x = 3"
    }
  ]
};

// Export for use in the assessment system
if (typeof module !== 'undefined' && module.exports) {
  module.exports = INTERACTIVE_QUESTION_EXAMPLES;
} else if (typeof window !== 'undefined') {
  window.INTERACTIVE_QUESTION_EXAMPLES = INTERACTIVE_QUESTION_EXAMPLES;
}
